<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ly.titc</groupId>
  <artifactId>pms-member-asset-parent</artifactId>
  <packaging>pom</packaging>
  <version>1.0.0</version>
  <modules>
    <module>pms-member-asset-com</module>
    <module>pms-member-asset-dal</module>
    <module>pms-member-asset-biz</module>
    <module>pms-member-asset-facade</module>
    <module>pms-member-asset-mediator</module>
    <module>pms-member-asset-interfaces</module>
    <module>pms-member-asset-dubbo-provider</module>
    <module>pms-member-asset-job</module>
  </modules>

  <properties>
    <!--##########################======springboot begin分割线=======###########################-->
    <!--springboot-->
    <spring-boot-dependencies.version>1.0.1-SNAPSHOT</spring-boot-dependencies.version>
    <!--##########################======springboot end分割线=========###########################-->

    <!--##########################======java begin分割线===============###########################-->
    <!--lombok -->
    <lombok.version>1.18.20</lombok.version>
    <!--mapstruct-->
    <mapstruct.version>1.4.2.Final</mapstruct.version>
    <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    <!--##########################======java end分割线=================###########################-->

    <!--##########################======titc begin分割线===============###########################-->
    <!--api-->
    <mdm-api.version>1.0.1-SNAPSHOT</mdm-api.version>
    <member-asset-interfaces.version>1.0.0-SNAPSHOT</member-asset-interfaces.version>
    <pms-ecrm-interfaces.version>1.0.1-SNAPSHOT</pms-ecrm-interfaces.version>
    <com-component-interfaces.version>1.0.0-SNAPSHOT</com-component-interfaces.version>
    <com-component-sdk.version>1.0.3-SNAPSHOT</com-component-sdk.version>
    <!--##########################======titc end分割线=================###########################-->
    <titc-common.version>1.0.10-SNAPSHOT</titc-common.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!--##########################======springboot begin分割线=======###########################-->
      <!--springboot dependencies -->
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-dependencies-pom</artifactId>
        <version>${spring-boot-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!--##########################======springboot end分割线=======#############################-->

      <!--##########################======titc begin分割线=============###########################-->
      <!--api-->
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-member-asset-interfaces</artifactId>
        <version>${member-asset-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>pms-ecrm-interfaces</artifactId>
        <version>${pms-ecrm-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>mdm-api</artifactId>
        <version>${mdm-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>com-component-interfaces</artifactId>
        <version>${com-component-interfaces.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>com-component-sdk</artifactId>
        <version>${com-component-sdk.version}</version>
      </dependency>
      <!--##########################======titc end分割线===============###########################-->
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>common</artifactId>
        <version>${titc-common.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <!-- 打包环境配置 -->
  <profiles>
    <profile>
      <id>test</id>
      <properties>
        <package.environment>test</package.environment>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>stage</id>
      <properties>
        <package.environment>stage</package.environment>
      </properties>
    </profile>
    <profile>
      <id>qa</id>
      <properties>
        <package.environment>qa</package.environment>
      </properties>
    </profile>
    <profile>
      <id>uat</id>
      <properties>
        <package.environment>uat</package.environment>
      </properties>
    </profile>
    <profile>
      <id>product</id>
      <properties>
        <package.environment>product</package.environment>
      </properties>
    </profile>
  </profiles>

  <repositories>
    <!-- &lt;!&ndash; ly.com maven mirror &ndash;&gt;-->
    <repository>
      <id>lyRepository</id>
      <name>lyRepository</name>
      <url>https://nexus.17usoft.com/repository/mvn-all/</url>
      <layout>default</layout>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>
</project>