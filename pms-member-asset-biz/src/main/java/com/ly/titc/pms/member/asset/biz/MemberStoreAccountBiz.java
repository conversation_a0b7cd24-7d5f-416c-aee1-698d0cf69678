package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dal.dao.MemberStoreAccountInfoDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-9 17:41
 */
@Slf4j
@Component
public class MemberStoreAccountBiz extends ServiceImpl<MemberStoreAccountInfoDao, MemberStoreAccountInfo> {
    @Resource
    private MemberStoreAccountInfoDao accountInfoDao;
    public MemberStoreAccountInfo getByMemberNo(String memberNo) {
        LambdaQueryWrapper<MemberStoreAccountInfo> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(MemberStoreAccountInfo::getMemberNo, memberNo);
         return  accountInfoDao.selectOne(lambdaQuery);
    }

    public List<MemberStoreAccountInfo> getByMemberNos(List<String> memberNos, String sdate, String edate) {
        LambdaQueryWrapper<MemberStoreAccountInfo> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.in(MemberStoreAccountInfo::getMemberNo, memberNos);
        lambdaQuery.ge(MemberStoreAccountInfo::getGmtCreate, sdate);
        lambdaQuery.le(MemberStoreAccountInfo::getGmtCreate, edate);
        return accountInfoDao.selectList(lambdaQuery);
    }

    /**
     * 计算总额和余额 根据版本号更新
     * @param capitalAmount
     * @param giftAmount
     * @param memberNo
     */

    public void calTotalRechargeAmount(BigDecimal capitalAmount, BigDecimal giftAmount, String memberNo) {
        MemberStoreAccountInfo accountInfo = getByMemberNo(memberNo);
        BigDecimal total = capitalAmount.add(giftAmount);
        if (accountInfo == null) {
            MemberStoreAccountInfo newAccountInfo = new MemberStoreAccountInfo();
            newAccountInfo.setMemberNo(memberNo);
            newAccountInfo.setTotalAmount(total);
            newAccountInfo.setTotalCapitalAmount(capitalAmount);
            newAccountInfo.setTotalGiftAmount(giftAmount);
            newAccountInfo.setTotalBalance(total);
            newAccountInfo.setTotalCapitalBalance(capitalAmount);
            newAccountInfo.setTotalGiftBalance(giftAmount);
            save(newAccountInfo);
            return;
        }
        accountInfo.setTotalAmount(total.add(accountInfo.getTotalAmount()));
        accountInfo.setTotalCapitalAmount(capitalAmount.add(accountInfo.getTotalCapitalAmount()));
        accountInfo.setTotalGiftAmount(giftAmount.add(accountInfo.getTotalGiftAmount()));
        accountInfo.setTotalBalance(total.add(accountInfo.getTotalBalance()));
        accountInfo.setTotalCapitalBalance(capitalAmount.add(accountInfo.getTotalCapitalBalance()));
        accountInfo.setTotalGiftBalance(giftAmount.add(accountInfo.getTotalGiftBalance()));
        LambdaUpdateWrapper<MemberStoreAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberStoreAccountInfo::getTotalAmount, accountInfo.getTotalAmount())
                .set(MemberStoreAccountInfo::getTotalCapitalAmount, accountInfo.getTotalCapitalAmount())
                .set(MemberStoreAccountInfo::getTotalGiftAmount, accountInfo.getTotalGiftAmount())
                .set(MemberStoreAccountInfo::getTotalBalance, accountInfo.getTotalBalance())
                .set(MemberStoreAccountInfo::getTotalCapitalBalance, accountInfo.getTotalCapitalBalance())
                .set(MemberStoreAccountInfo::getTotalGiftBalance, accountInfo.getTotalGiftBalance())
                .set(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() + 1)
                .eq(MemberStoreAccountInfo::getMemberNo, memberNo)
                .eq(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() );
        int affectedRows =   accountInfoDao.update(null, updateWrapper);
        if(affectedRows != 1) {
            throw new ServiceException(RespCodeEnum.Store_Lock_Error);
        }
    }


    /**
     * 计算已使用总额，根据版本号更新
     */
    public void calUsedAmount(BigDecimal capitalAmount, BigDecimal giftAmount, String memberNo) {
        MemberStoreAccountInfo accountInfo = getByMemberNo(memberNo);
        if (accountInfo == null) {
            throw new ServiceException(RespCodeEnum.Account_Not_Exist);
        }
        BigDecimal total = capitalAmount.add(giftAmount);
        accountInfo.setTotalUsedAmount(total.add(accountInfo.getTotalUsedAmount()));
        accountInfo.setTotalUsedCapitalAmount(capitalAmount.add(accountInfo.getTotalUsedCapitalAmount()));
        accountInfo.setTotalUsedGiftAmount(giftAmount.add(accountInfo.getTotalUsedGiftAmount()));
        accountInfo.setTotalBalance(accountInfo.getTotalBalance().subtract(total));
        accountInfo.setTotalCapitalBalance(accountInfo.getTotalCapitalBalance().subtract(capitalAmount));
        accountInfo.setTotalGiftBalance(accountInfo.getTotalGiftBalance().subtract(giftAmount));
        LambdaUpdateWrapper<MemberStoreAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberStoreAccountInfo::getTotalUsedAmount, accountInfo.getTotalUsedAmount())
                .set(MemberStoreAccountInfo::getTotalUsedCapitalAmount, accountInfo.getTotalUsedCapitalAmount())
                .set(MemberStoreAccountInfo::getTotalUsedGiftAmount, accountInfo.getTotalUsedGiftAmount())
                .set(MemberStoreAccountInfo::getTotalBalance, accountInfo.getTotalBalance())
                .set(MemberStoreAccountInfo::getTotalCapitalBalance, accountInfo.getTotalCapitalBalance())
                .set(MemberStoreAccountInfo::getTotalGiftBalance, accountInfo.getTotalGiftBalance())
                .set(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() + 1)
                .eq(MemberStoreAccountInfo::getMemberNo, memberNo)
                .eq(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() );
        int affectedRows =   accountInfoDao.update(null, updateWrapper);
        if(affectedRows != 1) {
            throw new ServiceException(RespCodeEnum.Store_Lock_Error);
        }
    }

    /**
     * 计算冻结总额，根据版本号更新
     */
    public void calFrozenAmount(BigDecimal capitalAmount, BigDecimal giftAmount, String memberNo) {
        MemberStoreAccountInfo accountInfo = getByMemberNo(memberNo);
        if (accountInfo == null) {
            throw new ServiceException(RespCodeEnum.Account_Not_Exist);
        }
        BigDecimal total = capitalAmount.add(giftAmount);
        accountInfo.setTotalFrozenAmount(total.add(accountInfo.getTotalFrozenAmount()));
        accountInfo.setTotalFrozenCapitalAmount(capitalAmount.add(accountInfo.getTotalFrozenCapitalAmount()));
        accountInfo.setTotalFrozenGiftAmount(giftAmount.add(accountInfo.getTotalFrozenGiftAmount()));
        accountInfo.setTotalBalance(accountInfo.getTotalBalance().subtract(total));
        accountInfo.setTotalCapitalBalance(accountInfo.getTotalCapitalBalance().subtract(capitalAmount));
        accountInfo.setTotalGiftBalance(accountInfo.getTotalGiftBalance().subtract(giftAmount));
        LambdaUpdateWrapper<MemberStoreAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberStoreAccountInfo::getTotalFrozenAmount, accountInfo.getTotalFrozenAmount())
                .set(MemberStoreAccountInfo::getTotalFrozenCapitalAmount, accountInfo.getTotalFrozenCapitalAmount())
                .set(MemberStoreAccountInfo::getTotalFrozenGiftAmount, accountInfo.getTotalFrozenGiftAmount())
                .set(MemberStoreAccountInfo::getTotalBalance, accountInfo.getTotalBalance())
                .set(MemberStoreAccountInfo::getTotalCapitalBalance, accountInfo.getTotalCapitalBalance())
                .set(MemberStoreAccountInfo::getTotalGiftBalance, accountInfo.getTotalGiftBalance())
                .set(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() + 1)
                .eq(MemberStoreAccountInfo::getMemberNo, memberNo)
                .eq(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() );
        int affectedRows =   accountInfoDao.update(null, updateWrapper);
        if(affectedRows != 1) {
            throw new ServiceException(RespCodeEnum.Store_Lock_Error);
        }
    }

    /**
     * 计算已使用总额，根据版本号更新
     */
    public void calRefundAmount(BigDecimal capitalAmount, BigDecimal giftAmount, String memberNo) {
        MemberStoreAccountInfo accountInfo = getByMemberNo(memberNo);
        if (accountInfo == null) {
            throw new ServiceException(RespCodeEnum.Account_Not_Exist);
        }
        BigDecimal total = capitalAmount.add(giftAmount);
        accountInfo.setTotalBalance(total.add(accountInfo.getTotalFrozenAmount()));
        accountInfo.setTotalCapitalBalance(capitalAmount.add(accountInfo.getTotalCapitalAmount()));
        accountInfo.setTotalGiftBalance(giftAmount.add(accountInfo.getTotalGiftAmount()));
        accountInfo.setTotalUsedAmount(accountInfo.getTotalUsedAmount().subtract(total));
        accountInfo.setTotalUsedCapitalAmount(accountInfo.getTotalUsedCapitalAmount().subtract(capitalAmount));
        accountInfo.setTotalUsedGiftAmount(accountInfo.getTotalUsedGiftAmount().subtract(giftAmount));
        LambdaUpdateWrapper<MemberStoreAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberStoreAccountInfo::getTotalBalance, accountInfo.getTotalBalance())
                .set(MemberStoreAccountInfo::getTotalCapitalBalance, accountInfo.getTotalCapitalBalance())
                .set(MemberStoreAccountInfo::getTotalGiftBalance, accountInfo.getTotalGiftBalance())
                .set(MemberStoreAccountInfo::getTotalUsedAmount, accountInfo.getTotalUsedAmount())
                .set(MemberStoreAccountInfo::getTotalUsedCapitalAmount, accountInfo.getTotalUsedCapitalAmount())
                .set(MemberStoreAccountInfo::getTotalUsedGiftAmount, accountInfo.getTotalUsedGiftAmount())
                .set(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() + 1)
                .eq(MemberStoreAccountInfo::getMemberNo, memberNo)
                .eq(MemberStoreAccountInfo::getVersion, accountInfo.getVersion() );
        int affectedRows =   accountInfoDao.update(null, updateWrapper);
        if(affectedRows != 1) {
            throw new ServiceException(RespCodeEnum.Store_Lock_Error);
        }
    }
}
