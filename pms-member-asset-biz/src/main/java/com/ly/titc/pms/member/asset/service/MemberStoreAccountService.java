package com.ly.titc.pms.member.asset.service;

import com.ly.titc.pms.member.asset.biz.MemberStoreAccountBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-9 17:43
 */
@Slf4j
@Service
public class MemberStoreAccountService {

    @Resource
    private MemberStoreAccountBiz storeAccountBiz;

    public MemberStoreAccountInfo getByMemberNo(String memberNo) {
        return storeAccountBiz.getByMemberNo(memberNo);
    }
}
