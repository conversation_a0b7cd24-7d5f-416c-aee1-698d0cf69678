package com.ly.titc.pms.member.asset.entity;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-19 11:42
 */
@Data
@Accessors
public class MemberPointReceiveRecordWrapper {

    /**
     * 获得积分记录
     */
   private  MemberPointReceiveRecord  receiveRecord;

    /**
     * 积分流水
     */
    private MemberPointRecord pointRecord;


    /**
     * 回滚的记录
     */
    private  MemberPointReceiveRecord  rollBackRecord;

}
