package com.ly.titc.pms.member.asset.service;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.biz.MemberStoreAccountBiz;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeRecordBiz;
import com.ly.titc.pms.member.asset.com.enums.RechargeTypeEnum;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dal.entity.dto.RechargeWrapper;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:38
 */
@Slf4j
@Service
public class MemberStoreRecordService extends MemberStoreRechargeRecordBiz {
    @Resource
    private MemberStoreAccountBiz storeAccountBiz;

    @Transactional(rollbackFor = Exception.class)
    public void saveRecords(RechargeWrapper wrapper) {
        MemberStoreRechargeRecord addRecord = wrapper.getAddRecord();
        if (addRecord != null) {
            this.save(addRecord);
        }
        MemberStoreRechargeRecord updateRecord = wrapper.getUpdateRecord();
        if (updateRecord != null) {
            if (wrapper.getVersion() == null) {
                throw new ServiceException("版本号不能为空", RespCodeEnum.CODE_500.getCode());
            }
            this.updateByBalance(updateRecord, wrapper.getVersion());
        }
        storeAccountBiz.calTotalRechargeAmount(wrapper.getCapitalAmount(), wrapper.getGiftAmount(), wrapper.getMemberNo());
    }

    public List<MemberStoreRechargeRecord> listRechargeRecords(List<String> tradeNoList, String memberNo) {
        return listByMemberNoAndTradeNoList(memberNo, tradeNoList, RechargeTypeEnum.RECHARGE.getType());
    }
}
