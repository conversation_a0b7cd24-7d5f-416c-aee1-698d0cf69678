package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.dal.dao.MemberPointReceiveConsumeMappingDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveConsumeMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-13 11:23
 */
@Slf4j
@Component
public class MemberPointReceiveConsumeMappingBiz extends ServiceImpl<MemberPointReceiveConsumeMappingDao, MemberPointReceiveConsumeMapping> {


    public List<MemberPointReceiveConsumeMapping> listByReceiveNo(String receiveRecordNo,List<String> consumeTypes,String memberNo) {

        return baseMapper.selectList(new LambdaQueryWrapper<MemberPointReceiveConsumeMapping>()
                .eq(MemberPointReceiveConsumeMapping::getReceiveRecordNo, receiveRecordNo)
                .eq(MemberPointReceiveConsumeMapping::getMemberNo, memberNo)
                .in(MemberPointReceiveConsumeMapping::getConsumeType, consumeTypes)
        );
    }

    public List<MemberPointReceiveConsumeMapping> listByReceiveRecordNo(String receiveRecordNo, String memberNo, Integer isConsume) {

        return baseMapper.selectList(new LambdaQueryWrapper<MemberPointReceiveConsumeMapping>()
                .eq(MemberPointReceiveConsumeMapping::getReceiveRecordNo, receiveRecordNo)
                .eq(MemberPointReceiveConsumeMapping::getMemberNo, memberNo)
                .eq(isConsume !=null,MemberPointReceiveConsumeMapping::getIsRollback, isConsume)
        );
    }

    public List<MemberPointReceiveConsumeMapping> listByConsumeRecordNo(String getConsumeRecordNo, String memberNo, Integer isConsume) {

        return baseMapper.selectList(new LambdaQueryWrapper<MemberPointReceiveConsumeMapping>()
                .eq(MemberPointReceiveConsumeMapping::getConsumeRecordNo, getConsumeRecordNo)
                .eq(MemberPointReceiveConsumeMapping::getMemberNo, memberNo)
                .eq(isConsume !=null,MemberPointReceiveConsumeMapping::getIsRollback, isConsume)
        );
    }
}
