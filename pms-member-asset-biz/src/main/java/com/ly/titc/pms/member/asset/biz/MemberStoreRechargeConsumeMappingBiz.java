package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeConsumeMappingDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-19 13:45
 */
@Slf4j
public class MemberStoreRechargeConsumeMappingBiz extends ServiceImpl<MemberStoreRechargeConsumeMappingDao, MemberStoreRechargeConsumeMapping> {

    @Resource
    private MemberStoreRechargeConsumeMappingDao mappingDao;


    public List<MemberStoreRechargeConsumeMapping> listByConsumeRecordNo(String memberNo,String consumeRecordNo) {
        return lambdaQuery().eq(MemberStoreRechargeConsumeMapping::getMemberNo, memberNo)
                .eq(MemberStoreRechargeConsumeMapping::getConsumeRecordNo, consumeRecordNo)
                .list();
    }

    public void batchUpdateByBalanceWithLock(List<MemberStoreRechargeConsumeMapping> records, String memberNo){
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        int expectedRows = records.size();

        int affectedRows =   mappingDao.batchUpdateByBalanceWithLock(records,memberNo);
        if (affectedRows != expectedRows) {
            // 存在乐观锁冲突或记录不存在
            throw new ServiceException(RespCodeEnum.Store_Lock_Error);
        }
    }

}
