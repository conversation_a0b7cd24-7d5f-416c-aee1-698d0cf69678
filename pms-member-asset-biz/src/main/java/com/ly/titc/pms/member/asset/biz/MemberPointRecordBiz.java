package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.dal.dao.MemberPointRecordDao;
import com.ly.titc.pms.member.asset.dal.entity.bo.PageMemberPointBo;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedPointDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author：rui
 * @name：MemberPointBiz
 * @Date：2024-12-9 11:44
 * @Filename：MemberPointBiz
 */
@Slf4j
@Component
public class MemberPointRecordBiz extends ServiceImpl<MemberPointRecordDao, MemberPointRecord> {
    @Resource
    private MemberPointRecordDao dao;


    public Page<MemberPointRecord> pageMemberPointRecord(PageMemberPointBo bo) {

        return baseMapper.selectPage(new Page<>(bo.getPageIndex(), bo.getPageSize()), Wrappers.lambdaQuery(MemberPointRecord.class)
                .eq(StringUtils.isNotEmpty(bo.getMemberNo()), MemberPointRecord::getMemberNo, bo.getMemberNo())
                .eq(StringUtils.isNotEmpty(bo.getMasterCode()), MemberPointRecord::getMasterCode, bo.getMasterCode())
                .eq(Objects.nonNull(bo.getMasterType()), MemberPointRecord::getMasterType, bo.getMasterType())
                .between(StringUtils.isNotEmpty(bo.getBeginTime()) && StringUtils.isNotEmpty(bo.getEndTime()), MemberPointRecord::getGmtCreate, bo.getBeginTime(), bo.getEndTime())
        );
    }


    /**
     * 查询消费记录总额
     * @param masterType
     * @param masterCode
     * @return
     */
    public MemberUsedPointDto getMasterTotalUsedPoint(String memberNo, Integer masterType, String masterCode, List<String> tradeTypes){
        return   dao.getMasterTotalUsedPoint(memberNo,masterType,masterCode,tradeTypes);
    }


    /**
     * 查询业务记录号
     */
    public MemberPointRecord getByBusinessNo(String businessNo, String businessType,String memberNo) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(MemberPointRecord.class)
                .eq(MemberPointRecord::getBusinessNo, businessNo)
                .eq(MemberPointRecord::getBusinessType, businessType)
                .eq(MemberPointRecord::getMemberNo, memberNo)
        );
    }

    public List<MemberPointRecord> listByRecordNos(String memberNo, List<String> recordNos){
        return baseMapper.selectList(Wrappers.lambdaQuery(MemberPointRecord.class)
                .eq(MemberPointRecord::getMemberNo, memberNo)
                .in(MemberPointRecord::getRecordNo, recordNos)
        );
    }



}
