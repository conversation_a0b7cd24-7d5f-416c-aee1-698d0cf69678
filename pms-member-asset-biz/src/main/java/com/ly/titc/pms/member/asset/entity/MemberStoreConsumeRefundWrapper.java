package com.ly.titc.pms.member.asset.entity;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@Builder
public class MemberStoreConsumeRefundWrapper {

    /**
     * 消费记录
     */
    private MemberStoreRechargeConsumeRecord originalRecord;

    /**
     * 消费退款记录
     */
    private MemberStoreRechargeConsumeRecord refundRecord;

    /**
     * 需要更新的充值记录
     */
    private List<MemberStoreRechargeRecord> needUpdateRechargeRecords;

    /**
     * 需要更新的消费记录mapping
     */
    private List<MemberStoreRechargeConsumeMapping> needUpdateConsumeMappings;

}
