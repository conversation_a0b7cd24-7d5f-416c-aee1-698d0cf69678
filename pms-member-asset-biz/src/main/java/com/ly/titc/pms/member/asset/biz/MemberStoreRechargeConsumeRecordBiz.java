package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeConsumeRecordDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:05
 */
@Slf4j
@Component
public class MemberStoreRechargeConsumeRecordBiz extends ServiceImpl<MemberStoreRechargeConsumeRecordDao,MemberStoreRechargeConsumeRecord> {
    @Resource
    private MemberStoreRechargeConsumeRecordDao dao;

    /**
     * 查询消费记录总额
     * @param masterType
     * @param masterCode
     * @return
     */
    public MemberUsedAmountDto getMasterTotalUsedAmount(String memberNo,Integer masterType, String masterCode, List<String> consumeTypes){
      return   dao.getMasterTotalUsedAmount(memberNo,masterType,masterCode,consumeTypes);
    }

    public MemberStoreRechargeConsumeRecord getByRecordNo(String memberNo,String consumeRecordNo){
        LambdaQueryWrapper<MemberStoreRechargeConsumeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberStoreRechargeConsumeRecord::getMemberNo,memberNo)
                .eq(MemberStoreRechargeConsumeRecord::getConsumeRecordNo,consumeRecordNo);
        return getOne(queryWrapper);
    }

    public MemberStoreRechargeConsumeRecord getByBizConsumeNo(String memberNo, String bizConsumeNo,String sourceSystem) {
        LambdaQueryWrapper<MemberStoreRechargeConsumeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberStoreRechargeConsumeRecord::getMemberNo,memberNo)
                .eq(MemberStoreRechargeConsumeRecord::getBizConsumeNo,bizConsumeNo)
                .eq(MemberStoreRechargeConsumeRecord::getSourceSystem,sourceSystem);
        return getOne(queryWrapper);
    }

    /**
     * 查询原单的退款记录信息
     */
    public List<MemberStoreRechargeConsumeRecord> getByOriginalRecordNo(String memberNo, String originalConsumeRecordNo) {
        LambdaQueryWrapper<MemberStoreRechargeConsumeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberStoreRechargeConsumeRecord::getMemberNo,memberNo)
                .eq(MemberStoreRechargeConsumeRecord::getOriginalConsumeRecordNo,originalConsumeRecordNo);
        return list(queryWrapper);
    }


}
