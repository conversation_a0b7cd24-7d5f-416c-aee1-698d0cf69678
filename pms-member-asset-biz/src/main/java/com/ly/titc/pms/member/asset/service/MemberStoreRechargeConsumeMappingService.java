package com.ly.titc.pms.member.asset.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeConsumeMappingBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberStoreRechargeConsumeMappingService
 * @Date：2024-12-6 11:29
 * @Filename：MemberStoreRechargeConsumeMappingService
 */
@Slf4j
@Service
public class MemberStoreRechargeConsumeMappingService extends MemberStoreRechargeConsumeMappingBiz {

    public List<MemberStoreRechargeConsumeMapping> listByRechargeRecordNoList(List<String> rechargeRecordNoList) {
        return baseMapper.selectList(new LambdaQueryWrapper<MemberStoreRechargeConsumeMapping>()
                .in(MemberStoreRechargeConsumeMapping::getRechargeRecordNo, rechargeRecordNoList));
    }

}
