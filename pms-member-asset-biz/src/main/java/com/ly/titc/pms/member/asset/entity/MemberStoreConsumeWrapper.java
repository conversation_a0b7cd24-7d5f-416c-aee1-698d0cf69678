package com.ly.titc.pms.member.asset.entity;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeFreezeDetail;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-19 16:36
 */
@Data
@Accessors(chain = true)
public class MemberStoreConsumeWrapper {

    /**
     * 消费充值mapping表
     */
    private List<MemberStoreRechargeConsumeMapping> consumeMappings;

    /**
     * 消费记录
     */
    private MemberStoreRechargeConsumeRecord consumeRecord;

    /**
     * 冻结记录
     */
    private MemberStoreRechargeFreezeDetail freezeDetail;
}
