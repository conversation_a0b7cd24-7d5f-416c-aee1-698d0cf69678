package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeRecordDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:06
 */
@Slf4j
public class MemberStoreRechargeRecordBiz extends ServiceImpl<MemberStoreRechargeRecordDao,MemberStoreRechargeRecord> {

    @Resource
    private MemberStoreRechargeRecordDao rechargeRecordDao;

    public MemberStoreRechargeRecord getByTradeNo(String tradeNo,String memberNo,String tradeType){
        LambdaQueryWrapper<MemberStoreRechargeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberStoreRechargeRecord::getTradeNo,tradeNo)
                .eq(MemberStoreRechargeRecord::getTradeType,tradeType)
                .eq(MemberStoreRechargeRecord::getMemberNo,memberNo);
        return getOne(queryWrapper);

    }


    /**
     * 更新余额 ，乐观锁
     * @param record
     * @param version
     */
    public void  updateByBalance(MemberStoreRechargeRecord record,Integer version){
        LambdaUpdateWrapper<MemberStoreRechargeRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberStoreRechargeRecord::getCapitalBalance,record.getCapitalBalance())
                .set(MemberStoreRechargeRecord::getGiftBalance,record.getGiftBalance())
                .set(MemberStoreRechargeRecord::getTotalBalance,record.getTotalBalance())
                .set(MemberStoreRechargeRecord::getModifyUser,record.getModifyUser())
                .set(MemberStoreRechargeRecord::getVersion,version+1)
                .eq(MemberStoreRechargeRecord::getMemberNo,record.getMemberNo())
                .eq(MemberStoreRechargeRecord::getRechargeRecordNo,record.getRechargeRecordNo())
                .eq(MemberStoreRechargeRecord::getVersion,version);
        int affectedRows = rechargeRecordDao.update(null,updateWrapper);
       if(affectedRows != 1) {
           throw new ServiceException(RespCodeEnum.Store_Lock_Error);
       }
    }

    public void batchUpdateByBalanceWithLock(List<MemberStoreRechargeRecord> records,String memberNo){
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        int expectedRows = records.size();

        int affectedRows =   rechargeRecordDao.batchUpdateByBalanceWithLock(records,memberNo);
        if (affectedRows != expectedRows) {
            // 存在乐观锁冲突或记录不存在
            throw new ServiceException(RespCodeEnum.Store_Lock_Error);
        }
    }

    /**
     * 查询余额大于0的记录列表
     * 注意 此时礼金过期的可能会被查出来
     * 本次计算余额时，发现过期礼金异步发MQ，余额中扣减，并给转移到过期礼金字段中
     */
    public List<MemberStoreRechargeRecord> listByBalanceGtZero(String memberNo) {
        LambdaQueryWrapper<MemberStoreRechargeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberStoreRechargeRecord::getMemberNo, memberNo)
                .gt(MemberStoreRechargeRecord::getTotalBalance, BigDecimal.ZERO);
        return list(queryWrapper);
    }

    public List<MemberStoreRechargeRecord> listByMemberNoAndTradeNoList(String memberNo, List<String> tradeNoList,String tradeType) {
        LambdaQueryWrapper<MemberStoreRechargeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberStoreRechargeRecord::getMemberNo, memberNo)
                .eq(MemberStoreRechargeRecord::getTradeType,tradeType)
                .in(MemberStoreRechargeRecord::getTradeNo, tradeNoList);
        return list(queryWrapper);
    }

}
