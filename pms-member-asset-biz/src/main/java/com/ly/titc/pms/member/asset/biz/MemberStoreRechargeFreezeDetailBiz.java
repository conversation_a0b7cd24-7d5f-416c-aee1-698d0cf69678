package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeConsumeRecordDao;
import com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeFreezeDetailDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeFreezeDetail;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:05
 */
@Slf4j
@Component
public class MemberStoreRechargeFreezeDetailBiz extends ServiceImpl<MemberStoreRechargeFreezeDetailDao, MemberStoreRechargeFreezeDetail> {


    public List<MemberStoreRechargeFreezeDetail> listByConsumeRecordNo(List<String> consumeRecordNo,String memberNo) {
        return baseMapper.selectList(new LambdaQueryWrapper<MemberStoreRechargeFreezeDetail>()
                .in(MemberStoreRechargeFreezeDetail::getConsumeRecordNo, consumeRecordNo)
                .eq(MemberStoreRechargeFreezeDetail::getMemberNo, memberNo));
    }

    public void unfreeze(MemberStoreRechargeFreezeDetail detail) {
        LambdaUpdateWrapper<MemberStoreRechargeFreezeDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberStoreRechargeFreezeDetail::getUnfreezeUser, detail.getUnfreezeUser())
                .set(MemberStoreRechargeFreezeDetail::getUnfreezeDate, detail.getUnfreezeDate())
                .set(MemberStoreRechargeFreezeDetail::getUnfreezeReason, detail.getUnfreezeReason())
                .eq(MemberStoreRechargeFreezeDetail::getConsumeRecordNo, detail.getConsumeRecordNo())
                .eq(MemberStoreRechargeFreezeDetail::getMemberNo, detail.getMemberNo());
        baseMapper.update(null, updateWrapper);
    }

    public MemberStoreRechargeFreezeDetail getByRecord(String consumeRecordNo,String memberNo){
        return baseMapper.selectOne(new LambdaQueryWrapper<MemberStoreRechargeFreezeDetail>()
                .eq(MemberStoreRechargeFreezeDetail::getConsumeRecordNo,consumeRecordNo)
                .eq(MemberStoreRechargeFreezeDetail::getMemberNo,memberNo)
        );
    }


}
