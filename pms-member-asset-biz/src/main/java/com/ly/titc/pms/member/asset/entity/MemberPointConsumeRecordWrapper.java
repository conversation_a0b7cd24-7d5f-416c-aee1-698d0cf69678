package com.ly.titc.pms.member.asset.entity;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-19 11:42
 */
@Data
@Accessors
public class MemberPointConsumeRecordWrapper {

    /**
     * 获得积分记录
     */
    private List<MemberPointReceiveRecord> receiveRecords;

    /**
     * 消费流水
     */
    private MemberPointConsumeRecord consumeRecord;


    /**
     * 回滚的记录
     */
    private  List<MemberPointReceiveConsumeMapping> mappings;

    /**
     * 积分记录
     */
    private MemberPointRecord pointRecord;


    /**
     * 需要更余额的积分记录
     */
    private List<MemberPointRecord> needUpdatePointRecords;

}
