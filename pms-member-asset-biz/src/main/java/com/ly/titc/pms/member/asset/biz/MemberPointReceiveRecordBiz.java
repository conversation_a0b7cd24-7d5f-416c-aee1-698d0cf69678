package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.com.enums.PonitReceiveTypeEnum;
import com.ly.titc.pms.member.asset.dal.dao.MemberPointReceiveRecordDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-13 11:23
 */
@Slf4j
@Component
public class MemberPointReceiveRecordBiz extends ServiceImpl<MemberPointReceiveRecordDao, MemberPointReceiveRecord> {


    /**
     * 查询业务记录号
     */
    public MemberPointReceiveRecord getReceiveByBusinessNo(String businessNo, String businessType, String memberNo) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(MemberPointReceiveRecord.class)
                .eq(MemberPointReceiveRecord::getBusinessNo, businessNo)
                .eq(MemberPointReceiveRecord::getBusinessType, businessType)
                .eq(MemberPointReceiveRecord::getMemberNo, memberNo)
                .eq(MemberPointReceiveRecord::getReceiveType, PonitReceiveTypeEnum.RECEIVE.getType())
        );
    }

    /**
     * 查询业务记录号
     */
    public MemberPointReceiveRecord getReceive(String businessNo, String businessType,String recordNo, String memberNo) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(MemberPointReceiveRecord.class)
                .eq(StringUtils.isNotEmpty(businessNo),MemberPointReceiveRecord::getBusinessNo, businessNo)
                .eq(StringUtils.isNotEmpty(businessType),MemberPointReceiveRecord::getBusinessType, businessType)
                .eq(StringUtils.isNotEmpty(recordNo),MemberPointReceiveRecord::getReceiveRecordNo, recordNo)
                .eq(MemberPointReceiveRecord::getMemberNo, memberNo)
                .eq(MemberPointReceiveRecord::getReceiveType, PonitReceiveTypeEnum.RECEIVE.getType())
        );
    }

    /**
     * 更新余额
     */

    public void updateBalance(MemberPointReceiveRecord record,Integer version){
        LambdaUpdateWrapper<MemberPointReceiveRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MemberPointReceiveRecord::getBalanceScore,record.getBalanceScore())
                .set(MemberPointReceiveRecord::getVersion,version+1)
                .eq(MemberPointReceiveRecord::getMemberNo,record.getMemberNo())
                .eq(MemberPointReceiveRecord::getReceiveRecordNo,record.getReceiveRecordNo())
                .eq(MemberPointReceiveRecord::getVersion,version);
        baseMapper.update(null,wrapper);
    }

    public List<MemberPointReceiveRecord> listByBalanceGtZero(String memberNo){
        return baseMapper.selectList(Wrappers.lambdaQuery(MemberPointReceiveRecord.class)
                .eq(MemberPointReceiveRecord::getMemberNo, memberNo)
                .gt(MemberPointReceiveRecord::getBalanceScore, 0)
        );
    }

}
