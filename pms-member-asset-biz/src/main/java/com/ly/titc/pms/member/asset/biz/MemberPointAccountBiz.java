package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.dal.dao.MemberPointAccountInfoDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-10 15:24
 */
@Slf4j
@Component
public class MemberPointAccountBiz extends ServiceImpl<MemberPointAccountInfoDao, MemberPointAccountInfo> {

    public MemberPointAccountInfo getByMemberNo(String memberNo){
        LambdaQueryWrapper<MemberPointAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberPointAccountInfo::getMemberNo, memberNo);
        return getOne(queryWrapper);
    }

    public List<MemberPointAccountInfo> getByMemberNoList(List<String> memberNoList){
        LambdaQueryWrapper<MemberPointAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MemberPointAccountInfo::getMemberNo, memberNoList);
        return list(queryWrapper);
    }

    public List<MemberPointAccountInfo> getByMemberNoListAndPeriod(List<String> memberNoList, String sdate, String edate){
        LambdaQueryWrapper<MemberPointAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MemberPointAccountInfo::getMemberNo, memberNoList);
        queryWrapper.ge(MemberPointAccountInfo::getGmtCreate, sdate)
                .le(MemberPointAccountInfo::getGmtCreate, edate);
        return list(queryWrapper);
    }

    /**
     * 计算总额和余额 根据版本号更新
     * @param score
     * @param memberNo
     */

    public void calTotalScore(Integer score, String memberNo) {
        MemberPointAccountInfo accountInfo = getByMemberNo(memberNo);
        if (accountInfo == null) {
            MemberPointAccountInfo newAccountInfo = new MemberPointAccountInfo();
            newAccountInfo.setMemberNo(memberNo);
            newAccountInfo.setTotalScore(score);
            newAccountInfo.setTotalScoreBalance(score);
            save(newAccountInfo);
            return;
        }
        accountInfo.setTotalScore(accountInfo.getTotalScore() + score);
        accountInfo.setTotalScoreBalance(accountInfo.getTotalScoreBalance() + score);
        LambdaUpdateWrapper<MemberPointAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberPointAccountInfo::getTotalScore, accountInfo.getTotalScore())
                .set(MemberPointAccountInfo::getTotalScoreBalance, accountInfo.getTotalScoreBalance())
                .set(MemberPointAccountInfo::getVersion, accountInfo.getVersion() + 1)
                .eq(MemberPointAccountInfo::getMemberNo, memberNo)
                .eq(MemberPointAccountInfo::getVersion, accountInfo.getVersion() );
        update(null, updateWrapper);
    }

    /**
     * 计算已使用积分 根据版本号更新
     */
    public void calUsedScore(Integer score, String memberNo) {
        MemberPointAccountInfo accountInfo = getByMemberNo(memberNo);
        if (accountInfo == null) {
            log.error("积分账户不存在{}", memberNo);
            return;
        }
        accountInfo.setTotalScoreBalance(accountInfo.getTotalScoreBalance() - score);
        accountInfo.setTotalUsedScore(accountInfo.getTotalUsedScore() + score);
        LambdaUpdateWrapper<MemberPointAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberPointAccountInfo::getTotalScoreBalance, accountInfo.getTotalScoreBalance())
                .set(MemberPointAccountInfo::getTotalUsedScore, accountInfo.getTotalUsedScore())
                .eq(MemberPointAccountInfo::getMemberNo, memberNo);
        update(null, updateWrapper);
    }

    /**
     * 计算已过期积分 根据版本号更新
     */
    public void calExpiredScore(Integer score, String memberNo,Integer version) {
        MemberPointAccountInfo accountInfo = getByMemberNo(memberNo);
        if (accountInfo == null) {
            log.error("积分账户不存在{}", memberNo);
            return;
        }
        accountInfo.setTotalScoreBalance(accountInfo.getTotalScoreBalance() - score);
        accountInfo.setTotalExpireScore(accountInfo.getTotalExpireScore() + score);
        LambdaUpdateWrapper<MemberPointAccountInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberPointAccountInfo::getTotalScoreBalance, accountInfo.getTotalScoreBalance())
                .set(MemberPointAccountInfo::getTotalExpireScore, accountInfo.getTotalExpireScore())
                .set(MemberPointAccountInfo::getVersion, version + 1)
                .eq(MemberPointAccountInfo::getMemberNo, memberNo)
                .eq(MemberPointAccountInfo::getVersion, version);
        update(null, updateWrapper);
    }
}
