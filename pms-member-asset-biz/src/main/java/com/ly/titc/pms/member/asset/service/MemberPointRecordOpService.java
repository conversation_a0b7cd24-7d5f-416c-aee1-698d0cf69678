package com.ly.titc.pms.member.asset.service;

import com.ly.titc.pms.member.asset.biz.MemberPointAccountBiz;
import com.ly.titc.pms.member.asset.biz.MemberPointConsumeRecordBiz;
import com.ly.titc.pms.member.asset.biz.MemberPointReceiveConsumeMappingBiz;
import com.ly.titc.pms.member.asset.biz.MemberPointReceiveRecordBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import com.ly.titc.pms.member.asset.entity.MemberPointConsumeRecordWrapper;
import com.ly.titc.pms.member.asset.entity.MemberPointReceiveRecordWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-19 11:31
 */
@Service
@Slf4j
public class MemberPointRecordOpService {
    @Resource
    private MemberPointReceiveRecordBiz  receiveRecordBiz;
    @Resource
    private MemberPointRecordService recordService;
    @Resource
    private MemberPointAccountBiz memberPointAccountBiz;
    @Resource
    private MemberPointConsumeRecordBiz consumeRecordBiz;
    @Resource
    private MemberPointReceiveConsumeMappingBiz  consumeMappingBiz;

    public MemberPointReceiveRecord getReceiveByBusinessNo(String businessNo, String businessType, String memberNo) {
        return receiveRecordBiz.getReceiveByBusinessNo(businessNo, businessType, memberNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveReceive(MemberPointReceiveRecordWrapper wrapper) {
        MemberPointReceiveRecord record = wrapper.getReceiveRecord();
        receiveRecordBiz.save(wrapper.getReceiveRecord());
        recordService.save(wrapper.getPointRecord());
        memberPointAccountBiz.calTotalScore(record.getScore(),record.getMemberNo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveReceiveRollBack(MemberPointReceiveRecordWrapper wrapper) {
        MemberPointReceiveRecord rollBackRecord = wrapper.getRollBackRecord();
        MemberPointReceiveRecord receiveRecord  = wrapper.getReceiveRecord();
        //更新获得记录余额
        receiveRecordBiz.updateBalance(receiveRecord,receiveRecord.getVersion());
        receiveRecordBiz.save(rollBackRecord);
        recordService.save(wrapper.getPointRecord());
        MemberPointRecord pointRecord = new MemberPointRecord();
        pointRecord.setMemberNo(receiveRecord.getMemberNo());
        pointRecord.setRecordNo(receiveRecord.getReceiveRecordNo());
        pointRecord.setBalanceScore(receiveRecord.getBalanceScore());
        pointRecord.setModifyUser(receiveRecord.getModifyUser());
        //原则上版本号两个记录的版本号是一致的
        pointRecord.setVersion(receiveRecord.getVersion());
        recordService.updateBalance(pointRecord,pointRecord.getVersion());
        memberPointAccountBiz.calTotalScore(rollBackRecord.getScore(),rollBackRecord.getMemberNo());
    }
    @Transactional(rollbackFor = Exception.class)
    public void saveConsume(MemberPointConsumeRecordWrapper recordWrapper){
        List<MemberPointReceiveRecord> receiveRecords = recordWrapper.getReceiveRecords();
        MemberPointConsumeRecord consumeRecord = recordWrapper.getConsumeRecord();
        List<MemberPointReceiveConsumeMapping> mappings = recordWrapper.getMappings();
        MemberPointRecord pointRecord = recordWrapper.getPointRecord();
        List<MemberPointRecord> needUpdatePointRecords = recordWrapper.getNeedUpdatePointRecords();
        receiveRecords.forEach(receiveRecord -> {
            receiveRecordBiz.updateBalance(receiveRecord,receiveRecord.getVersion());
        });
        needUpdatePointRecords.forEach(record -> {
            recordService.updateBalance(record,record.getVersion());
        });
        consumeRecordBiz.save(consumeRecord);
        consumeMappingBiz.saveBatch(mappings);
        recordService.save(pointRecord);
        memberPointAccountBiz.calUsedScore(consumeRecord.getScore(),consumeRecord.getMemberNo());
    }
}
