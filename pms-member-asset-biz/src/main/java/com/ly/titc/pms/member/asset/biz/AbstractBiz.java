package com.ly.titc.pms.member.asset.biz;

import com.ly.titc.common.factory.RedisFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @classname AbstractBiz
 * @descrition
 * @since 2021/4/20 下午8:18
 */
public abstract class AbstractBiz<T> {

    @Autowired
    protected RedisFactory redisFactory;

	/**
	 * 删除redis key
	 *
	 * @param keys
	 */
	protected void deleteRedisKey(List<String> keys) {
		if (CollectionUtils.isEmpty(keys)) {
			return;
		}
		keys.parallelStream().forEach(key -> redisFactory.del(key));
	}

}
