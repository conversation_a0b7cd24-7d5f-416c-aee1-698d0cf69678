package com.ly.titc.pms.member.asset.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.asset.biz.MemberPointRecordBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import org.mapstruct.Mapper;

/**
 * @Author：rui
 * @name：MemberPointService
 * @Date：2024-12-9 11:43
 * @Filename：MemberPointService
 */
@Mapper(componentModel = "spring")
public class MemberPointRecordService extends MemberPointRecordBiz {

    /**
     * 更新余额
     */

    public void updateBalance(MemberPointRecord record, Integer version){
        LambdaUpdateWrapper<MemberPointRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MemberPointRecord::getBalanceScore,record.getBalanceScore())
                .set(MemberPointRecord::getVersion,version+1)
                .eq(MemberPointRecord::getMemberNo,record.getMemberNo())
                .eq(MemberPointRecord::getRecordNo,record.getRecordNo())
                .eq(MemberPointRecord::getVersion,version);
        baseMapper.update(null,wrapper);
    }
}
