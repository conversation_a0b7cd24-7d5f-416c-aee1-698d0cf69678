package com.ly.titc.pms.member.asset.biz;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.titc.pms.member.asset.dal.dao.MemberPointConsumeRecordDao;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointConsumeRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-13 11:23
 */
@Slf4j
@Component
public class MemberPointConsumeRecordBiz extends ServiceImpl<MemberPointConsumeRecordDao, MemberPointConsumeRecord> {


    /**
     * 查询业务记录号
     */
    public MemberPointConsumeRecord getConsume(String businessNo, String businessType, String consumeNo,String memberNo, List<String> consumeTypes) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(MemberPointConsumeRecord.class)
                .eq(StringUtils.isNotEmpty(businessNo),MemberPointConsumeRecord::getBusinessNo, businessNo)
                .eq(StringUtils.isNotEmpty(businessType),MemberPointConsumeRecord::getBusinessType, businessType)
                .eq(StringUtils.isNotEmpty(consumeNo),MemberPointConsumeRecord::getConsumeRecordNo, consumeNo)
                .eq(MemberPointConsumeRecord::getMemberNo, memberNo)
                .in(MemberPointConsumeRecord::getConsumeType, consumeTypes)
        );
    }

    /**
     * 根据原始业务号查询消费记录
     */
    public List<MemberPointConsumeRecord> listConsumeByOriginalNo(String originalNo,String memberNo) {
        return baseMapper.selectList(Wrappers.lambdaQuery(MemberPointConsumeRecord.class)
                .eq(MemberPointConsumeRecord::getOriginalConsumeRecordNo, originalNo)
                .eq(MemberPointConsumeRecord::getMemberNo, memberNo)
        );
    }
}
