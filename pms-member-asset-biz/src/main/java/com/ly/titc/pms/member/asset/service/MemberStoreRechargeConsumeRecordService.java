package com.ly.titc.pms.member.asset.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.pms.member.asset.biz.MemberStoreAccountBiz;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeConsumeMappingBiz;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeConsumeRecordBiz;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeFreezeDetailBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.entity.MemberStoreConsumeRefundWrapper;
import com.ly.titc.pms.member.asset.entity.MemberStoreConsumeWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-19 16:34
 */
@Slf4j
@Service
public class MemberStoreRechargeConsumeRecordService extends MemberStoreRechargeConsumeRecordBiz {
    @Resource
    private MemberStoreRechargeConsumeMappingBiz mappingBiz;
    @Resource
    private MemberStoreAccountBiz storeAccountBiz;
    @Resource
    private MemberStoreRechargeFreezeDetailBiz freezeDetailBiz;

    @Resource
    private MemberStoreRecordService storeRecordService;

    @Transactional(rollbackFor = Exception.class)
    public void saveConsumeRecord(MemberStoreConsumeWrapper wrapper) {
        MemberStoreRechargeConsumeRecord consumeRecord = wrapper.getConsumeRecord();
        //保存消费记录
        save(consumeRecord);
        //保存消费充值mapping
        mappingBiz.saveBatch(wrapper.getConsumeMappings());
        storeAccountBiz.calUsedAmount(consumeRecord.getCapitalAmount(), consumeRecord.getGiftAmount(), consumeRecord.getMemberNo());
    }

    public void saveConsumeRefundRecord(MemberStoreConsumeRefundWrapper wrapper) {
        MemberStoreRechargeConsumeRecord refundRecord = wrapper.getRefundRecord();
        save(refundRecord);
        //更新消费记录mapping
        mappingBiz.batchUpdateByBalanceWithLock(wrapper.getNeedUpdateConsumeMappings(),refundRecord.getMemberNo());
        //更新储值记录mapping
        storeRecordService.batchUpdateByBalanceWithLock(wrapper.getNeedUpdateRechargeRecords(),refundRecord.getMemberNo());
        //计算账户余额
        storeAccountBiz.calRefundAmount(refundRecord.getCapitalAmount(),refundRecord.getGiftAmount(),refundRecord.getMemberNo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveFreezeRecord(MemberStoreConsumeWrapper wrapper) {
        MemberStoreRechargeConsumeRecord consumeRecord = wrapper.getConsumeRecord();
        //保存消费记录
        save(consumeRecord);
        //保存消费充值mapping
        mappingBiz.saveBatch(wrapper.getConsumeMappings());
        freezeDetailBiz.save(wrapper.getFreezeDetail());
        storeAccountBiz.calFrozenAmount(consumeRecord.getCapitalAmount(), consumeRecord.getGiftAmount(), consumeRecord.getMemberNo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void unFreezeRecord(MemberStoreConsumeWrapper wrapper){
        MemberStoreRechargeConsumeRecord consumeRecord = wrapper.getConsumeRecord();
        freezeDetailBiz.unfreeze(wrapper.getFreezeDetail());
        storeAccountBiz.calFrozenAmount(consumeRecord.getCapitalAmount().negate(), consumeRecord.getGiftAmount().negate(), consumeRecord.getMemberNo());

    }

    public Page<MemberStoreRechargeConsumeRecord> page(String memberNo, String masterCode, Integer masterType, String platformChannel, String beginTime, String endTime,
                                                       Integer pageIndex, Integer pageSize, String consumeType) {

        return baseMapper.selectPage(new Page<>(pageIndex, pageSize), Wrappers.lambdaQuery(MemberStoreRechargeConsumeRecord.class)
                .eq(StringUtils.isNotEmpty(memberNo), MemberStoreRechargeConsumeRecord::getMemberNo, memberNo)
                .eq(StringUtils.isNotEmpty(masterCode), MemberStoreRechargeConsumeRecord::getMasterCode, masterCode)
                .eq(Objects.nonNull(masterType), MemberStoreRechargeConsumeRecord::getMasterType, masterType)
                .eq(StringUtils.isNotEmpty(platformChannel), MemberStoreRechargeConsumeRecord::getPlatformChannel, platformChannel)
                .eq(StringUtils.isNotEmpty(consumeType), MemberStoreRechargeConsumeRecord::getConsumeType, consumeType)
                .between(StringUtils.isNotEmpty(beginTime) && StringUtils.isNotEmpty(endTime), MemberStoreRechargeConsumeRecord::getGmtCreate, beginTime, endTime));
    }
}
