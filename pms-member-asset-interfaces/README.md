member-interfaces:member interfaces

##返回体规范##

1.返回数据必须使用com.ly.titc.common.entity.Response

2.分页数据返回体使用com.ly.titc.common.entity.Pageable

3.业务请求体统一使用xxxReq

4.业务返回体统一使用xxxResp

5.业务封装数据使用dto

##接口规范##

1.单个查询:getByxxx或者getDetailByxxx

2.列表查询:listByxxx或者listDetailsByxxx

3.下拉列表查询:selectByxxx

4.分页查询:pageByxxx

5.单个新增信息:addxxx

6.批量新增信息:batchAddxxx

7.保存信息:savexxx(无新增/有修改)

8.单个修改信息:updatexxx

9.批量修改信息:batchUpdatexxx

10.单个删除信息:deletexxx

11.批量删除信息:batchDeletexxx

12.分组查询信息:groupingByxxx

13.上传:uploadxxx

14.下载:downloadxxx

15.注册:registerxxx

16.登录:login

17.登出:logout

18.参数验证使用@Valid(javax.validation.Valid)

@NotBlank:String 不是 null 且去除两端空白字符后的长度（trimmed length）大于 0

@NotNull:CharSequence, Collection, Map 和 Array 对象不能是 null, 但可以是空集（size = 0）

@NotEmpty://CharSequence, Collection, Map 和 Array 对象不能是 null 并且相关对象的 size 大于 0

##方法枚举##

统一定义path&desc