package com.ly.titc.pms.member.asset.dubbo.entity.response.point;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:17
 */
@Data
@Accessors(chain = true)
public class MemberPointRecordResultResp {

    /**
     * 记录号
     */
    private String recordNo;
    /**
     * 获得记录号
     */
    private String receiveNo;

    /**
     * 消费记录号
     */
    private String consumeNo;
}
