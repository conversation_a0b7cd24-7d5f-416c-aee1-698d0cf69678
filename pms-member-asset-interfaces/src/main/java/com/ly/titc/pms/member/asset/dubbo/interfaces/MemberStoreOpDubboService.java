package com.ly.titc.pms.member.asset.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeCalResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRollBackResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreRecordOPResultResp;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值充值dubbo服务
 * @since 2024-11-13 14:14
 */
public interface MemberStoreOpDubboService {

    /**
     * 保存充值记录
     * 使用充值交易号幂等
     */
    Response<MemberStoreRecordOPResultResp> recharge(@Valid MemberStoreRechargeReq req);

    /**
     * 充值后回滚 （仅支持整单退款）
     */
    Response<MemberStoreRecordOPResultResp> rechargeRollback(@Valid MemberStoreRechargeRollBackReq req);

    /**
     * 消费会员储值
     */
    Response<MemberStoreRecordOPResultResp> consumeStore(@Valid MemberStoreConsumeReq req);

    /**
     * 消费金额预计算
     */
    Response<MemberStoreConsumeCalResp> consumeStoreCal(@Valid MemberStoreConsumePreCalReq req);
    /**
     * 消费会员储值退款
     */
    Response<MemberStoreConsumeRollBackResp> consumeRollback(@Valid MemberStoreConsumeRollBackReq req);

    /**
     * 储值资产冻结
     */
    Response<MemberStoreRecordOPResultResp> freeze(@Valid MemberStoreFreezeReq req);


    /**
     * 解冻
     */
    Response<MemberStoreRecordOPResultResp> unFreeze(@Valid UnfreezeConsumeRecordNoReq req);

}
