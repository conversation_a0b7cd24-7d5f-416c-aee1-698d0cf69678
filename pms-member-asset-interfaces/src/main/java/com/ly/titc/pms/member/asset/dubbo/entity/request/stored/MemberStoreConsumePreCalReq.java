package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors
public class MemberStoreConsumePreCalReq extends BaseMemberReq {

    /**
     * 主体类型
     */
    @NotNull(message = "主体类型不能为空")
    private Integer masterType;

    /**
     * 主体code
     */
    @NotEmpty(message = "主体code不能为空")
    private String masterCode;
    /**
     * 酒馆组code ELONG (冗余)
     * 充值类型是ELONG时必传
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     * 充值主体是集团和门店时必传
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     * 充值主体是门店时必传
     */
    private String hotelCode;

    /**
     * 平台
     */
    @NotEmpty(message = "平台不能为空")
    private String platformChannel;


    /**
     * 收银场景，支付调用时必传
     */
    private String cashierScene;

    /**
     * 本次消费金额
     */
    @NotNull(message = "本次消费金额不能为空")
    private BigDecimal amount;


}
