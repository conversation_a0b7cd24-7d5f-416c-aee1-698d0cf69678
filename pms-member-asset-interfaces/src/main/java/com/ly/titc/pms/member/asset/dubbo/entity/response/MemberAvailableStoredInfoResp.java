package com.ly.titc.pms.member.asset.dubbo.entity.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 会员可用储值信息
 *
 * <AUTHOR>
 * @date 2024/11/7 17:02
 */
@Data
public class MemberAvailableStoredInfoResp {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 可用金额
     */
    private BigDecimal availableAmount;

    /**
     * 可用赠送金额
     */
    private BigDecimal availableGiveAmount;

}
