package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:40
 */
@Data
@Accessors
public class MemberStoreFreezeReq extends BaseMemberReq {
    /**
     * 主体类型
     */
    @NotNull(message = "主体类型不能为空")
    private Integer masterType;

    /**
     * 主体code
     */
    @NotEmpty(message = "主体code不能为空")
    private String masterCode;
    /**
     * 酒馆组code ELONG (冗余)
     * 充值类型是ELONG时必传
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     * 充值主体是集团和门店时必传
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     * 充值主体是门店时必传
     */
    private String hotelCode;

    /**
     * 平台
     */
    @NotEmpty(message = "平台不能为空")
    private String platformChannel;

    /**
     * 本金金额
     */
    @NotNull(message = "本金金额不能为空")
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    @NotNull(message = "礼金金额不能为空")
    private BigDecimal giftAmount;

    /**
     * 冻结有效期 yyyy-mm-dd
     */
    private String freezeDate;
    /**
     * 是否长期冻结 0否 1是
     */
    @NotNull(message = "是否长期冻结不能为空")
    private Integer isFreezeLong;


    /**
     * 消费描述
     */
    @NotEmpty(message = "商品描述不能为空")
    private String consumeDesc;
    /**
     * 消费类型：FREEZE :冻结
     */
    @NotEmpty(message = "消费类型不能为空")
    private String consumeType;

    /**
     * 业务消费号（订单号）(来源系统+业务消费号幂等)
     */
    @NotEmpty(message = "业务消费号不能为空")
    private String bizConsumeNo;


    /**
     * 来源系统：会员-MEMBER，商品部-SHOP 餐饮-FOOD 微订房-WEBOOKNG 收银台——CASHIER
     */
    @NotEmpty(message = "来源系统不能为空")
    private String sourceSystem;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;

}
