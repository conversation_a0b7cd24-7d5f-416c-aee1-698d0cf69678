package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.MemberStoreUsageRuleModeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.RechargeTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:17
 */
@Data
@Accessors(chain = true)
public class MemberStoreRechargeRollBackReq extends BaseMemberReq {

    /**
     * 充值主体类型 1:集团 2:门店 3:酒馆
     */
    @NotNull(message = "充值主体类型不能为空")
    @LegalEnum(methodName = "getType",message = "主体类型不正确", target = MasterTypeEnum.class)
    private Integer masterType;

    /**
     * 充值主体CODE ELONG 集团编码 门店编码
     */
    @NotEmpty(message = "充值主体CODE不能为空")
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     * 充值类型是ELONG时必传
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     * 充值主体是集团和门店时必传
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     * 充值主体是门店时必传
     */
    private String hotelCode;

    /**
     * 订单交易号（充值订单号）
     */
    @NotEmpty(message = "订单交易号不能为空")
    private String tradeNo;

//    /**
//     * 原始充值交易号（退款时必传）
//     */
//    @NotEmpty(message = "原始充值交易号不能为空")
//    private String originalTradeNo;

    /**
     * 备注
     */
    @NotEmpty(message = "充值退款不能为空")
    private String remark;


    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
