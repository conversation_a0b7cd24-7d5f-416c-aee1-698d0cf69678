package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BasePageMemberReq;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:44
 */
@Data
public class PageMemberStoreConsumeMemberReq extends BasePageMemberReq {

    /**
     * 归属类型
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 消费类型：pay:支付，freeze冻结, refund 退款
     */
    @NotNull(message = "消费类型不能为空")
    private String consumeType;

    /**
     * 消费渠道
     */
    private String platformChannel;

}
