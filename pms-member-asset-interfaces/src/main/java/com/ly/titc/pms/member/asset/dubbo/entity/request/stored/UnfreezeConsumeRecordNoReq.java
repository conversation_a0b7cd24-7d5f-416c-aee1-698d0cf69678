package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-3 14:02
 */
@Data
@Accessors(chain = true)
public class UnfreezeConsumeRecordNoReq extends BaseMemberReq {

    @NotEmpty(message = "记录号不能不为")
    private String consumeRecordNo;

    /**
     * 解冻原因
     */
    private String unfreezeReason;

    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
