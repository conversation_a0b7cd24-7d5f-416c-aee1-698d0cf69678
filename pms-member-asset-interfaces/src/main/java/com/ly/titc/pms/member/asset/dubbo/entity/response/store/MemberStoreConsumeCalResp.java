package com.ly.titc.pms.member.asset.dubbo.entity.response.store;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-26 19:10
 */
@Data
@Accessors(chain = true)
public class MemberStoreConsumeCalResp {

    /**
     * 使用储值本金
     */
    private BigDecimal usedCapitalAmount;

    /**
     * 使用储值礼金
     */
    private BigDecimal usedGiftAmount;

    /**
     * 余额是否充足
     */
    private Boolean isEnough;
}
