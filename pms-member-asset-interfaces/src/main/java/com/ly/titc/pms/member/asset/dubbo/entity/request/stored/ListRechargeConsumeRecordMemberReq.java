package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：ListRechargeConsumeRecord
 * @Date：2024-12-9 10:24
 * @Filename：ListRechargeConsumeRecord
 */
@Data
public class ListRechargeConsumeRecordMemberReq extends BaseMemberReq {

    private List<String> tradeNoList;
}
