package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:28
 */
@Data
@Accessors(chain = true)
public class GetMemberTotalBalanceMemberReq extends BaseMemberReq {

    /**
     * 使用储值的主体类型
     */
    @NotNull(message = "主体类型不能为空")
    private Integer masterType;

    /**
     * 主体code
     */
    /**
     * 使用储值的主体code
     */
    @NotEmpty(message = "主体code不能为空")
    private String masterCode;


}
