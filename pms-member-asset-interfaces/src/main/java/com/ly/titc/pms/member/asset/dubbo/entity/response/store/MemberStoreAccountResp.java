package com.ly.titc.pms.member.asset.dubbo.entity.response.store;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 15:58
 */
@Data
@Accessors(chain = true)
public class MemberStoreAccountResp {

    /**
     * 总储值信息
     */
    private MemberTotalAmountResp totalAmountResp;

    /**
     * 主体和平台渠道可用信息
     */
    private MemberMasterAmountResp masterAmountResp;
}
