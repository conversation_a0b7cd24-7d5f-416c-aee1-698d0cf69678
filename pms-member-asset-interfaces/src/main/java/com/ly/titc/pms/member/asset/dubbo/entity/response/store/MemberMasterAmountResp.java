package com.ly.titc.pms.member.asset.dubbo.entity.response.store;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:28
 */
@Data
@Accessors(chain = true)
public class MemberMasterAmountResp {
    /**
     * 充值使用类型
     */
    private Integer masterType;

    /**
     * 充值使用类型编码
     */
    private String masterCode;


    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 本店+平台渠道总剩余可用余额
     */
    private BigDecimal usableTotalBalance;
    /**
     * 本店+平台渠道总剩余可用本金
     */
    private BigDecimal usableTotalCapitalBalance ;

    /**
     * 本店+平台渠道总剩余可用礼金
     */
    private BigDecimal usableTotalGiftBalance ;

    /**
     * 本店总已使用（本店下的所有渠道）
     */
    private BigDecimal usedTotalAmount ;

    /**
     * 本店总已使用本金（本店下的所有渠道）
     */
    private BigDecimal usedTotalCapitalBalance ;

    /**
     * 本店总已使用礼金（本店下的所有渠道）
     */
    private BigDecimal usedTotalGiftAmount ;
}
