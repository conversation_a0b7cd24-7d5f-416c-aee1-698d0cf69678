package com.ly.titc.pms.member.asset.dubbo.entity.response.point;

import lombok.Data;

/**
 * 会员总积分信息
 *
 * <AUTHOR>
 * @date 2024/11/7 19:52
 */
@Data
public class MemberTotalPointResp {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 总积分
     */
    private Integer totalScore = 0;

    /**
     * 总积分余额
     */
    private Integer totalScoreBalance =0;

    /**
     * 总已过期积分
     */
    private Integer totalExpireScore =0;

    /**
     * 总已使用积分
     */
    private Integer totalUsedScore=0;

}
