package com.ly.titc.pms.member.asset.dubbo.interfaces;


import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 积分操作服务
 * @since 2024-11-13 14:14
 */
public interface MemberPointOpDubboService {

    /**
     * 获得积分 （调整加积分）
     */
    Response<MemberRecordOPResultResp> receive(@Valid ReceiveMemberPointReq req);

    /**
     * 获得后撤回积分
     */
    Response<MemberRecordOPResultResp> receiveRollback(@Valid ReceiveRollBackMemberPointReq req);

    /**
     * 消费积分 （支付 或调整扣减）
     */
    Response<MemberRecordOPResultResp> consume(@Valid ConsumeMemberPointReq req);

    /**
     *消费撤回(退款) 等收银台调用
     */
    Response<MemberRecordOPResultResp> consumeRollback(@Valid  ConsumeRollBackMemberPointReq req);

    /**
     * 根据业务单号或者积分记录号查询积分消费记录
     */
    Response<List<MemberPointsFlowInfoResp>> listConsumeRecords(@Valid ListMemberPointConsumeForBusinessReq req);
}
