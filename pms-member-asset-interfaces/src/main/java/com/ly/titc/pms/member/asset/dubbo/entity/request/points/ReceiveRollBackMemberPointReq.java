package com.ly.titc.pms.member.asset.dubbo.entity.request.points;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetBusinessTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 19:46
 */
@Data
@Accessors(chain = true)
public class ReceiveRollBackMemberPointReq extends BaseMemberReq {
    /**
     * 积分主体类型   1:集团 2:门店 3:酒馆
     * 本条记录操作的主体
     */
    @NotNull(message = "积分主体类型")
    private Integer masterType;

    /**
     * 积分主体CODE ELONG 集团编码 门店编码
     */
    @NotEmpty(message = "积分主体不能为空")
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;


    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    @LegalEnum(methodName = "getType",message = "业务类型不正确", target = AssetBusinessTypeEnum.class)
    private String businessType;

    /**
     * 业务订单编号
     */
    @NotEmpty(message = "业务订单编号不能为空")
    private String businessNo;


    /**
     * 积分记录号 （业务单号和积分记录二选一）
     */
    private String receiverNo;


    /**
     * 积分数
     * 获得撤回份数 （可部分退回）
     */
    @NotNull(message = "积分数不能为空")
    private Integer score;

    /**
     * 备注 原因
     */
    @NotEmpty(message = "积分调整原因不能为空")
    private String remark;

    @NotEmpty(message = "操作人不能为空")
    private String operator;



}
