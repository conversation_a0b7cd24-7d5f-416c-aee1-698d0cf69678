package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:44
 */
@Data
@Accessors(chain = true)
public class ListMemberStoreConsumeByBusinessNoReq extends BaseMemberReq {


    private List<String> tradeNoList;
}
