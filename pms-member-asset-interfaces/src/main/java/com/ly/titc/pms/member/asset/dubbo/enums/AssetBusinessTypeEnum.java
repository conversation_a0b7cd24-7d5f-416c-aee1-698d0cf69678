package com.ly.titc.pms.member.asset.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 19:48
 */
@AllArgsConstructor
@Getter
public enum AssetBusinessTypeEnum {
    ROOM("ROOM","客房"),
    MEMBER("MEMBER","会员"),
    SHOP("SHOP","商品部"),
    SPM("SPM","营销活动"),
    ;

    private String type;

    private String desc;

    public static AssetBusinessTypeEnum getByType(String tradeType) {
        for (AssetBusinessTypeEnum typeEnum : AssetBusinessTypeEnum.values()) {
            if (typeEnum.getType().equals(tradeType)) {
                return typeEnum;
            }
        }
        return null;
    }
}
