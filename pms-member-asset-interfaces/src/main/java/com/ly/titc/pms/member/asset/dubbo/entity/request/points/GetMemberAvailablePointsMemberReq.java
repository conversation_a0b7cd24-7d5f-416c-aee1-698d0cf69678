package com.ly.titc.pms.member.asset.dubbo.entity.request.points;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询会员可用积分
 *
 * <AUTHOR>
 * @date 2024/11/7 19:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetMemberAvailablePointsMemberReq extends BaseMemberReq {



    /**
     * 平台（门店、艺龙会、微订房）
     */
    private String platform;

    /**
     * 集团
     */
    private String blocCode;

    /**
     * 门店
     */
    private String hotelCode;

    /**
     * 使用场景
     */
    private String scene;
}
