package com.ly.titc.pms.member.asset.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.ListMemberStoreConsumeByBusinessNoReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.ListRechargeConsumeRecordMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.PageMemberStoreConsumeMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值消费Dubbo服务
 * @since 2024-11-13 14:33
 */
public interface MemberStoreRechargeConsumeDubboService {


    /**
     * 查询指定业务单号的使用记录
     */
    Response<MemberStoreConsumeRecordResp> listByBusinessNo(@Valid ListMemberStoreConsumeByBusinessNoReq req);


    /**
     * 分页查询会员储值消费记录
     */
    Response<Pageable<MemberStoreConsumeRecordResp>> page(@Valid PageMemberStoreConsumeMemberReq req);

    /**
     * 查询会员充值记录
     */
    Response<List<MemberTradeConsumeRecordResp>> listRechargeConsumeRecord(@Valid ListRechargeConsumeRecordMemberReq req);
}
