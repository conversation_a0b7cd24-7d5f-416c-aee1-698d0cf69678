package com.ly.titc.pms.member.asset.dubbo.entity.request.points;

import com.ly.titc.pms.member.asset.dubbo.entity.request.BasePageMemberReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页查询会员积分流水
 *
 * <AUTHOR>
 * @date 2024/11/7 19:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageMemberPointsFlowMemberReq extends BasePageMemberReq {

    private String masterCode;

    private Integer masterType;

    private String beginTime;

    private String endTime;

}
