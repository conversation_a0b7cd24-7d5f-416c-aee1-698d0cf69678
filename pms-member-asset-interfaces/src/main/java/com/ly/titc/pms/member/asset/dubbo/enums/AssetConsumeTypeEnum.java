package com.ly.titc.pms.member.asset.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 19:48
 */
@AllArgsConstructor
@Getter
public enum AssetConsumeTypeEnum {
    PAY("PAY","支付"),
    REFUND("REFUND","退款"),

    ADJUST("ADJUST","调整"),

    FREEZE("FREEZE","冻结"),
    ;

    private String type;

    private String desc;

    public static AssetConsumeTypeEnum getByType(String tradeType) {
        for (AssetConsumeTypeEnum typeEnum : AssetConsumeTypeEnum.values()) {
            if (typeEnum.getType().equals(tradeType)) {
                return typeEnum;
            }
        }
        return null;
    }
}
