package com.ly.titc.pms.member.asset.dubbo.entity.response.store;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-29 11:41
 */
@Data
@Accessors(chain = true)
public class MemberStoreRecordOPResultResp {


    /**
     * 消费流水号
     */
    private String consumeNo;

    /**
     * 消费完成时间
     */
    private String payedTime;


}
