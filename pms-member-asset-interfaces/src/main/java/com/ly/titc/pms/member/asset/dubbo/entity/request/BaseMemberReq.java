package com.ly.titc.pms.member.asset.dubbo.entity.request;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname BaseReq
 * @descrition
 * @since 2021/3/10 上午10:25
 */
@Data
@Accessors(chain = true)
public class BaseMemberReq {

	/**
	 * 请求追踪id
	 */
	private String trackingId ;

	@NotEmpty(message = "会员号不能为空")
	private String memberNo;

}
