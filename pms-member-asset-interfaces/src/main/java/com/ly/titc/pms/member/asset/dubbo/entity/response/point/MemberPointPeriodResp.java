package com.ly.titc.pms.member.asset.dubbo.entity.response.point;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: luyan
 * @create: 2025-06-30 11:50
 **/
@Data
public class MemberPointPeriodResp {

    private List<MemberPointPeriodDto> memberPointPeriodList;

    @Data
    public static class MemberPointPeriodDto {

        private String memberNo;

        private Integer totalScore;
    }
}
