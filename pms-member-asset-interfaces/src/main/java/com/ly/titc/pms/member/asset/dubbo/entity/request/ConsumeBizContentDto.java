package com.ly.titc.pms.member.asset.dubbo.entity.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@Data
@Accessors(chain = true)
public class ConsumeBizContentDto {

    /**
     * 业务类型：会员-MEMBER，商品部-SHOP 餐饮-FOOD 微订房-WEBOOKNG
     * （账单明细需要）
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;


    /**
     * 业务单号 会员单号，商品部单号，餐饮单号 ,微订房订单号
     * 非业务支付单号，是业务订单号 （账单明细需要）
     */
    @NotBlank(message = "业务单号不能为空")
    private String bizOrderNo;


    /**
     * ——业务支付订单中获取
     * 产品信息 产品信息 json格式[] uniqueNo code name price num
     * 比如 本单支付的购买的产品明细 比如 商品购买的 可乐 一瓶 10元
     * 商品部订单不能为空
     * 账单明细需要展示
     */
    private List<MemberBizProductDto> bizProductDtoList;
}
