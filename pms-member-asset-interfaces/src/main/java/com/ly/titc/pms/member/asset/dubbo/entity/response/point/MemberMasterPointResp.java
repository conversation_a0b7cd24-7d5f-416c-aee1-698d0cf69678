package com.ly.titc.pms.member.asset.dubbo.entity.response.point;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:28
 */
@Data
@Accessors(chain = true)
public class MemberMasterPointResp {
    /**
     * 使用类型
     */
    private Integer masterType;

    /**
     * 使用类型编码
     */
    private String masterCode;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 本店+平台渠道总剩余可用余额
     */
    private Integer usableTotalScore;

    /**
     * 本店总使用积分（本店下的所有渠道）
     */
    private Integer usedTotalUsedScore ;


}
