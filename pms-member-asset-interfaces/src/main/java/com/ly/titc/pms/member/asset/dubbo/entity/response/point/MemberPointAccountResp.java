package com.ly.titc.pms.member.asset.dubbo.entity.response.point;

import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberMasterAmountResp;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 15:58
 */
@Data
@Accessors(chain = true)
public class MemberPointAccountResp {

    /**
     * 总储值信息
     */
    private MemberTotalPointResp totalPointResp;

    /**
     * 主体和平台渠道可用信息
     */
    private MemberMasterPointResp masterPointResp;
}
