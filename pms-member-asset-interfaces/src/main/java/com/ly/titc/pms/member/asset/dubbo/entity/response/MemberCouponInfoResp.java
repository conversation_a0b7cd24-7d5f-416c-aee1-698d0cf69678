package com.ly.titc.pms.member.asset.dubbo.entity.response;

import lombok.Data;

/**
 * 会员优惠券
 *
 * <AUTHOR>
 * @date 2024/11/7 19:31
 */
@Data
public class MemberCouponInfoResp {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 优惠券编号
     */
    private String couponNo;

    /**
     * 发放批次号
     */
    private String batchNo;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券类型
     */
    private Integer couponType;

    /**
     * 券值
     */
    private String couponValue;

    /**
     * 券值类型 1-固定金额 2-折扣率 3-延长小时 4-延长小时点 5-券后房价
     */
    private String couponValueType;

    /**
     * 生效日期
     */
    private String startDate;

    /**
     * 生效日期
     */
    private String endDate;

    /**
     * 使用日期
     */
    private String useDate;

    /**
     * 优惠券使用说明
     */
    private String content;

    /**
     * 优惠券使用提醒
     */
    private String tips;

    /**
     * 使用状态（0 未使用 1 已核销 2 已过期）
     */
    private Integer useState;

    /**
     * 发放平台
     */
    private String platform;

}
