package com.ly.titc.pms.member.asset.dubbo.entity.response.store;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-29 11:41
 */
@Data
@Accessors(chain = true)
public class MemberStoreConsumeRollBackResp {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 退款记录号
     */
    private String refundRecordNo;


}
