package com.ly.titc.pms.member.asset.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-25 9:40
 */
@Getter
@AllArgsConstructor
public enum PointActionItemEnum {

    /**
     * 日结加积分、日租房加积分、预定担保扣积分、积分兑换、日结noshow扣积分、
     * 日结发展会员积分、会员自动升级、会员手动升级、超出有效期积分清零、
     * 活动积分、网站积分、系统导入积分、会员充值送积分、优惠券礼包赠送积分
     */

    DAILY_SETTLE_ADD("DAILY_SETTLE_ADD", "日结加积分"),
    DAILY_RENTAL_HOUSE_ADD("DAILY_RENTAL_HOUSE_ADD", "日租房加积分"),
    RESERVATION_GUARANTEE_SUB("RESERVATION_GUARANTEE_SUB", "预定担保扣积分"),

    REDEEM("REDEEM", "积分兑换"),
    DAILY_SETTLE_NOSHOW("DAILY_SETTLE_NOSHOW", "日结noshow扣积分"),
    DAILY_DEVELOP_POINT("DAILY_DEVELOP_POINT", "日结发展会员积分"),
    MEMBER_AUTO_UPGRADE("MEMBER_AUTO_UPGRADE", "会员自动升级"),
    MEMBER_MANUAL_UPGRADE("MEMBER_MANUAL_UPGRADE", "会员手动升级"),
    CLEAR_POINT("CLEAR_POINT", "超出有效期积分清零"),

    ACTIVITY_POINT("ACTIVITY_POINT", "活动积分"),
    WEBSITE_POINT("WEBSITE_POINT", "网站积分"),
    SYS_IMPORT_POINT("SYS_IMPORT_POINT", "系统导入积分"),
    RECHARGE_POINT("RECHARGE_POINT", "会员充值送积分"),
    COUPON_GIFT_POINT("MULTI", "优惠券礼包赠送积分"),

    ;

    private String actionItem;

    private String actionItemDesc;
}
