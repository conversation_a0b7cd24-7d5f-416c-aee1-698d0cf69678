package com.ly.titc.pms.member.asset.dubbo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-20 11:51
 */
@AllArgsConstructor
@Getter
public enum TradeOpetatorTypeEnum {

    PAY("pay","支付"),
    REFUND("refund","退款"),

    PREPAY("prePay","预授权"),

    PREPAY_CANCEL("prePayCancel","预授权撤销"),

    PREPAY_COMPLETE("prePayComplete","预授权完成"),

    PREPAY_COMPLETE_CANCEL("prePayCompleteCancel","预授权完成撤销"),

    ;


    private String type;

    private String desc;

    public static TradeOpetatorTypeEnum getByType(String tradeType) {
        for (TradeOpetatorTypeEnum tradeOpetatorTypeEnum : TradeOpetatorTypeEnum.values()) {
            if (tradeOpetatorTypeEnum.getType().equals(tradeType)) {
                return tradeOpetatorTypeEnum;
            }
        }
       return null;
    }
}
