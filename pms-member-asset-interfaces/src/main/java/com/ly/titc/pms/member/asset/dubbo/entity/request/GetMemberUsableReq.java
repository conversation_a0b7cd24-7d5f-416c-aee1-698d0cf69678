package com.ly.titc.pms.member.asset.dubbo.entity.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:29
 */
@Data
@Accessors(chain = true)
public class GetMemberUsableReq extends BaseMemberReq{
    /**
     * 使用储值的主体类型
     */
    @NotNull(message = "主体类型不能为空")
    private Integer masterType;


    /**
     * 使用储值的主体code
     */
    @NotEmpty(message = "主体code不能为空")
    private String masterCode;


    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    @NotEmpty(message = "使用平台不能为空")
    private String platformChannel;
}
