package com.ly.titc.pms.member.asset.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTotalAmountResp;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-9 14:08
 */
public interface MemberStoreDubboService {
    /**
     * 查询会员的总账户信息
     * 不区分平台和使用方
     * CRM端呈现
     */
    Response<MemberTotalAmountResp> getTotalAccountAmount(@Valid BaseMemberReq req);

    /**
     * 查询会员的指定平台和使用方的可用资产
     * 会同时返回总资产 暂不考虑冻结资产
     * PMS端使用
     */
    Response<MemberStoreAccountResp> getUsableMasterAccount(@Valid GetMemberUsableReq req);
}
