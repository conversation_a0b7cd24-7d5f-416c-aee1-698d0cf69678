package com.ly.titc.pms.member.asset.dubbo.entity.request.stored;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetBusinessTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:40
 */
@Data
@Accessors
public class MemberStoreConsumeReq extends BaseMemberReq {
    /**
     * 主体类型
     */
    @NotNull(message = "主体类型不能为空")
    private Integer masterType;

    /**
     * 主体code
     */
    @NotEmpty(message = "主体code不能为空")
    private String masterCode;
    /**
     * 酒馆组code ELONG (冗余)
     * 充值类型是ELONG时必传
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     * 充值主体是集团和门店时必传
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     * 充值主体是门店时必传
     */
    private String hotelCode;

    /**
     * 平台
     */
    @NotEmpty(message = "平台不能为空")
    private String platformChannel;

    /**
     * 本次消费金额
     */
    @NotNull(message = "本次消费金额不能为空")
    private BigDecimal amount;

    /**
     * 消费描述
     */
    @NotEmpty(message = "商品描述不能为空")
    private String goodsDes;

    /**
     * 消费类型：pay:支付，prePay预授权
     */
    @NotEmpty(message = "消费类型不能为空")
    private String consumeType;
    /**
     * 业务线唯一号
     */
    @NotEmpty(message = "业务线唯一号不能为空")
    private String businessNo;

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    @NotEmpty(message = "业务类型不能为空")
    @LegalEnum(methodName = "getType",message = "业务类型不正确", target = AssetBusinessTypeEnum.class)
    private String businessType;

    /**
     * 业务请求体
     */
    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;

}
