package com.ly.titc.pms.member.asset.dubbo.entity.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@Accessors (chain = true)
public class MemberBizProductDto {

    /**
     * 产品的唯一编号
     */
    private String uniqueNo;
    /**
     * 产品的code
     */
     private String code ;
    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品单价
     */
    private BigDecimal price;
    /**
     * 产品数量
     */

     private Integer num;

    /**
     *单价*数量 = 金额
     */

    private BigDecimal amount;

}
