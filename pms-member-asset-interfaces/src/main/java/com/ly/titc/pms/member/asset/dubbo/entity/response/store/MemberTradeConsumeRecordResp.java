package com.ly.titc.pms.member.asset.dubbo.entity.response.store;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author：rui
 * @name：MemberRechargeConsumeRecord
 * @Date：2024-12-9 11:18
 * @Filename：MemberRechargeConsumeRecord
 */
@Data
public class MemberTradeConsumeRecordResp {

    /**
     * 订单号
     */
    private String tradeNo;

    /**
     * 已消费储值金额
     */
    private BigDecimal consumeAmount;

    /**
     * 已消费礼金金额
     */
    private BigDecimal consumeGiftAmount;

    /**
     * 储值本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 储值礼金
     */
    private BigDecimal giftAmount;

    /**
     * 赠送礼金过期时间
     */
    private String giftExpireDate;

}