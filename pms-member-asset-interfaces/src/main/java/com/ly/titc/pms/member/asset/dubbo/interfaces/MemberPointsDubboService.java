package com.ly.titc.pms.member.asset.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BatchMemberPeriodReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BatchMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员积分Dubbo服务
 *
 * <AUTHOR>
 * @date 2024/11/7 16:30
 */
public interface MemberPointsDubboService {

    /**
     * 查询会员总积分
     *
     * @param req
     * @return
     */
    Response<MemberTotalPointResp> getTotalAccountPoints(@Valid BaseMemberReq req);

    /**
     * 查询多个会员的总积分
     * @param req
     * @return
     */
    Response<List<MemberTotalPointResp>> getTotalAccountPointsList(@Valid BatchMemberReq req);

    /**
     *
     * @param req
     * @return
     */
    Response<MemberPointPeriodResp> getTotalAccountPointsPeriodList(@Valid BatchMemberPeriodReq req);

    /**
     * 查询会员可用积分
     *
     * @param req
     * @return
     */
    Response<MemberPointAccountResp> getUsableMasterAccount(@Valid GetMemberUsableReq req);


    /**
     * 分页查询会员积分流水
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberPointsFlowInfoResp>> pageMemberPointsFlow(@Valid PageMemberPointsFlowMemberReq request);
}
