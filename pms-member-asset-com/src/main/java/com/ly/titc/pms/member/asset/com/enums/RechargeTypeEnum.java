package com.ly.titc.pms.member.asset.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 充值交易类型
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-20 11:51
 */
@AllArgsConstructor
@Getter
public enum RechargeTypeEnum {

    RECHARGE("RECHARGE","充值"),
    RECHARGE_REFUND("RECHARGE_REFUND","充值退款"),


    ;


    private String type;

    private String desc;

    public static RechargeTypeEnum getByType(String tradeType) {
        for (RechargeTypeEnum tradeOpetatorTypeEnum : RechargeTypeEnum.values()) {
            if (tradeOpetatorTypeEnum.getType().equals(tradeType)) {
                return tradeOpetatorTypeEnum;
            }
        }
       return null;
    }
}
