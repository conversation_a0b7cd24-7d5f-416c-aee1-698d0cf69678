package com.ly.titc.pms.member.asset.com.enums;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2021/9/3 11:49
 */
public enum StateEnum {

    OPEN(1,"有效"),
    CLOSE(0,"无效");

    private Integer state;
    private String desc;
    StateEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
