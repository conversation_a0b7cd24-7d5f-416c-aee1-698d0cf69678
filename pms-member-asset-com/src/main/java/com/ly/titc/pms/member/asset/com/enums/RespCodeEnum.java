package com.ly.titc.pms.member.asset.com.enums;


import com.ly.titc.common.ifs.IResponseCode;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @classname RespCodeEnum
 * @descrition
 * @since 2023/1/6 15:56
 */
public enum RespCodeEnum implements IResponseCode {

    /**
     * 通用
     */
    CODE_200("成功", "200"),
    CODE_400("参数非法", "400"),
    CODE_401("无操作权限", "401"),
    CODE_500("服务器异常，请稍后重试", "500"),

    /**
     * 储值
     */
    CODE_1001("交易号已存在", "1001"),


    CODE_1002("原交易单不存在", "1002"),


    CODE_1003("退款后余额小于0", "1003") ,

    CODE_1004("当前场景配置了多个使用规则，无法计算可用余额，请检查储值使用规则", "1004") ,

    CODE_1005("储值卡余额不足", "1005") ,
    CODE_1006("已被使用不允许退", "1006") ,
    CODE_1007("不是当前归属，无权限操作", "1007"),

    CODE_1008("已退款不可重复退", "1008"),
    CODE_1009("当前场景无法使用储值，请维护储值使用规则", "1009") ,

    CODE_1010("储值卡本金余额不足", "1010") ,

    CODE_1011("储值卡礼金余额不足", "1011") ,
    CODE_1012("冻结记录不存在", "1012") ,
    CODE_1013("已解冻，请勿重复操作", "1013") ,

    CODE_1014("当前场景未配置储值使用规则，请维护后再进行消费", "10014") ,

    /**
     * 积分
     */
    CODE_2001("积分余额不足", "2001") ,
    CODE_2002("当前场景配置了多个使用规则，无法计算可用积分，请检查积分使用规则", "2002") ,
    CODE_2003("当前渠道和来源不允许使用积分", "2003") ,

    CODE_2004("当前场景未配置积分使用规则，无法进行积分扣减", "2004") ,
    CODE_2005("当前场景不可使用积分，无法进行积分扣减，如需使用可修改积分使用配置", "2005") ,


    ;

    private String code;
    private String desc;

    RespCodeEnum(String desc, String code) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static RespCodeEnum getByCode(String code) {
        return Arrays.stream(RespCodeEnum.values())
                .filter(item -> {
                    return item.getCode().equals(code);
                })
                .findFirst().orElse(RespCodeEnum.CODE_500);
    }
}
