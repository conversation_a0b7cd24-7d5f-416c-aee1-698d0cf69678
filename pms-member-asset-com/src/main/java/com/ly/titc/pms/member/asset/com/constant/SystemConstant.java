package com.ly.titc.pms.member.asset.com.constant;

/**
 * <AUTHOR>
 * @title: SystemConstant
 * @projectName member
 * @description: 系统常量
 * @date 2023/11/14 10:20
 */
public interface SystemConstant {

    /**
     * 唯一版本
     */
    String UNIQUE_VERSION = "UNIQUE_VERSION";

    /**
     * 充值锁
     */
    String RECHARGE_LOCK = "MEMBER_ASSET_RECHARGE_LOCK";

    /**
     * 充值消费锁
     */
    String RECHARGE_CONSUME_LOCK = "MEMBER_ASSET_RECHARGE_CONSUME_LOCK";

    /**
     * 充值记录前缀
     */
    String RECHARGE_NO_PREFIX = "MR";


    /**
     * 积分记录前缀
     */
    String POINT_NO_PREFIX = "MP";

    /**
     * 积分锁
     */
    String POINT_LOCK = "MEMBER_ASSET_POINT_LOCK";

}
