package com.ly.titc.pms.member.asset.com.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 19:48
 */
@AllArgsConstructor
@Getter
public enum PonitReceiveTypeEnum {
    RECEIVE("RECEIVE","获得"),
    RECOVERY("RECOVERY","撤回"),;

    private String type;

    private String desc;

    public static PonitReceiveTypeEnum getByType(String tradeType) {
        for (PonitReceiveTypeEnum typeEnum : PonitReceiveTypeEnum.values()) {
            if (typeEnum.getType().equals(tradeType)) {
                return typeEnum;
            }
        }
        return null;
    }
}
