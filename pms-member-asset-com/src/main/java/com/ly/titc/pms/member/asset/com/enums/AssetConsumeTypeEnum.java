package com.ly.titc.pms.member.asset.com.enums;

/**
 * 消费类型
 *
 * @Author：rui
 * @name：ConsumeTypeEnum
 * @Date：2024-12-6 14:09
 * @Filename：ConsumeTypeEnum
 */
public enum AssetConsumeTypeEnum {

    PAY("PAY","支付"),
    REFUND("REFUND","退款"),

    ADJUST("ADJUST","调整"),

    FREEZE("FREEZE","冻结"),
    ;

    private String code;

    private String desc;


    AssetConsumeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

}
