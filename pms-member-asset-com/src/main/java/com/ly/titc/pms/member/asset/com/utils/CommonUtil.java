package com.ly.titc.pms.member.asset.com.utils;

import cn.hutool.core.util.IdUtil;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.util.LocalDateTimeUtil;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @classname CommonUtil
 * @descrition
 * @since 2019/11/26 14:45
 */
public class CommonUtil {

    /**
     * concat key
     *
     * @param args
     * @return
     */
    public static String concat(Object... args) {

        if (null == args || args.length == 0) {
            return "";
        }
        StringJoiner sj = new StringJoiner(Constant.STRING_LINE_UNDER);
        for (Object arg : args) {
            sj.add(String.valueOf(arg));
        }
        return sj.toString();
    }

    /**
     * generate unique no
     *
     * @return
     */
    public static String generateUniqueNo(String prefix) {
        long id = IdUtil.getSnowflake(0, 0).nextId();
        return prefix + id;
    }

    public static String timestampToString(Timestamp timestamp) {
        if (Objects.isNull(timestamp)) {
            return "";
        }
        // 将Timestamp转换为Instant
        LocalDateTime localDateTime = timestamp.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        return LocalDateTimeUtil.formatByNormalDateTime(localDateTime);
    }
}
