package com.ly.titc.pms.member.asset.dal.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员储值账户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberStoreAccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 充值主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 充值主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     *  会员总储值（本金余额+赠送余额）
     */
    private BigDecimal totalAmount;

    /**
     * 累计充值储值本金
     */
    private BigDecimal totalCapitalAmount;

    /**
     * 累计充值礼金
     */
    private BigDecimal totalGiftAmount;

    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance;

    /**
     * 总本金余额
     */
    private BigDecimal totalCapitalBalance;

    /**
     * 总赠送余额
     */
    private BigDecimal totalGiftBalance;

    /**
     * 总过期礼金金额
     */
    private BigDecimal totalExpireGiftAmount;

    /**
     *  总使用储值（本金余额+赠送余额）
     */
    private BigDecimal totalUsedAmount;

    /**
     * 总使用本金
     */
    private BigDecimal totalUsedCapitalAmount;

    /**
     * 总使用礼金金额
     */
    private BigDecimal totalUsedGiftAmount;

    /**
     *  总冻结储值（本金余额+赠送余额）
     */
    private BigDecimal totalFrozenAmount;

    /**
     * 总冻结本金
     */
    private BigDecimal totalFrozenCapitalAmount;

    /**
     * 总冻结礼金金额
     */
    private BigDecimal totalFrozenGiftAmount;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createtime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updatetime;


}
