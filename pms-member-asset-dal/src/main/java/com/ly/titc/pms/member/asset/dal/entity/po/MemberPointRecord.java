package com.ly.titc.pms.member.asset.dal.entity.po;

import com.baomidou.mybatisplus.annotation.*;

import java.sql.Timestamp;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员积分流水记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberPointRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 记录号
     */
    private String recordNo;

    /**
     * 积分主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 积分主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    private String platformChannel;

    /**
     * 操作类型：RECEIVE 获得 CONSUME 消耗
     */
    private String actionType;

    /**
     * 操作单号
     */
    private String actionNo;

    /**
     * 交易类型 PAY:支付 REFUND:退款 ADJUST:调整
     */
    private String tradeType;

    /**
     * 原记录号
     */
    private String originalBusinessNo;

    /**
     * 积分项目：日结加积分、日租房加积分、预定担保扣积分、积分兑换...
     */
    private String actionItem;

    /**
     * 积分项目描述
     */
    private String actionItemDesc;

    /**
     * 积分数
     */
    private Integer score;

    /**
     * 剩余积分(积分过期时使用剩余积分过期)
     */
    private Integer balanceScore;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 使用场景
     */
    private String scene;

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP
     */
    private String businessType;

    /**
     * 业务订单编号
     */
    private String businessNo;

    /**
     * 业务信息
     */
    private String businessNote;

    /**
     * 备注（原因）
     */
    private String remark;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createTime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updateTime;


}
