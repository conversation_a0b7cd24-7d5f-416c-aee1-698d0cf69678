package com.ly.titc.pms.member.asset.dal.dao;

import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedPointDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员积分流水记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
public interface MemberPointRecordDao extends BaseMapper<MemberPointRecord> {

    MemberUsedPointDto getMasterTotalUsedPoint(@Param("memberNo")String memberNo, @Param("masterType") Integer masterType, @Param("masterCode") String masterCode,
                                               @Param("tradeTypes")List<String> tradeTypes);
}
