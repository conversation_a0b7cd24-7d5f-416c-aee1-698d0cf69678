package com.ly.titc.pms.member.asset.dal.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员储值无效记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberStoreRechargeInvalidRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 充值记录号
     */
    private String rechargeRecordNo;

    /**
     * 充值主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 充值主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 资产类型 store 储值 point 积分
     */
    private String assetType;

    /**
     * 无效类型 expire:过期
     */
    private String invalidType;

    /**
     * 无效金额
     */
    private BigDecimal amount;

    /**
     * 无效数量
     */
    private Integer score;

    /**
     * 无效时间（过期时为过期时间）
     */
    private String invalidDate;

    /**
     * 无效原因
     */
    private String reason;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createtime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updatetime;


}
