package com.ly.titc.pms.member.asset.dal.dao;

import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员储值消费记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface MemberStoreRechargeConsumeRecordDao extends BaseMapper<MemberStoreRechargeConsumeRecord> {

    MemberUsedAmountDto getMasterTotalUsedAmount(@Param("memberNo")String memberNo,@Param("masterType") Integer masterType, @Param("masterCode") String masterCode,
                                                 @Param("consumeTypes")List<String> consumeTypes);

}
