package com.ly.titc.pms.member.asset.dal.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员储值充值记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberStoreRechargeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 充值记录号
     */
    private String rechargeRecordNo;

    /**
     * 充值主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 充值主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 交易类型 pay:充值，refund：退款
     */
    private String tradeType;

    /**
     * 订单交易号（充值订单号，退款订单号）
     */
    private String tradeNo;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    private String platformChannel;

    /**
     * 储值本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 储值礼金
     */
    private BigDecimal giftAmount;

    /**
     * 储值总额（储值本金-储值礼金）
     */
    private BigDecimal totalAmount;

    /**
     * 本金余额
     */
    private BigDecimal capitalBalance;

    /**
     * 赠送余额
     */
    private BigDecimal giftBalance;

    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance;

    /**
     * 充值活动code
     */
    private String activityCode;

    /**
     * 赠送礼金过期时间
     */
    private String giftExpireDate;

    /**
     * 使用规则模式
     */
    private String usageRuleMode;

    /**
     * 使用规则ID
     */
    private String usageRuleId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createTime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updateTime;


}
