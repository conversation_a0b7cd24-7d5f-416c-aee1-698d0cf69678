package com.ly.titc.pms.member.asset.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员冻结记录详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberStoreRechargeFreezeDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 消费主体类型  1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 消费主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 消费记录号
     */
    private String consumeRecordNo;

    /**
     * 冻结有效期 yyyy-mm-dd
     */
    private String freezeDate;

    /**
     * 是否长期冻结 0 否 1 是
     */
    private Integer isFreezeLong;

    /**
     * 解冻人 todo 冻结状态
     */
    private String unfreezeUser;

    /**
     * 解冻日期
     */
    private String unfreezeDate;

    /**
     * 解冻原因
     */
    private String unfreezeReason;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createtime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updatetime;


}
