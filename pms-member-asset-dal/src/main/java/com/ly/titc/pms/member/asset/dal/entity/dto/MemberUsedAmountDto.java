package com.ly.titc.pms.member.asset.dal.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值使用余额信息
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class MemberUsedAmountDto {

    /**
     * 充值使用类型
     */
    private Integer masterType;

    /**
     * 充值使用类型编码
     */
    private String masterCode;


    /**
     * 本店总已使用（本店下的所有渠道）
     */
    private BigDecimal usedTotalAmount = BigDecimal.ZERO;

    /**
     * 本店总已使用本金（本店下的所有渠道）
     */
    private BigDecimal usedTotalCapitalAmount = BigDecimal.ZERO;

    /**
     * 本店总已使用礼金（本店下的所有渠道）
     */
    private BigDecimal usedTotalGiftAmount = BigDecimal.ZERO;


}
