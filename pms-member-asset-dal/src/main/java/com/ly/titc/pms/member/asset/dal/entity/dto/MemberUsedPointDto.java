package com.ly.titc.pms.member.asset.dal.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-9 19:52
 */
@Data
@Accessors(chain = true)
public class MemberUsedPointDto {
    /**
     * 充值使用类型
     */
    private Integer masterType;

    /**
     * 充值使用类型编码
     */
    private String masterCode;
    /**
     * 总余额（本金余额+赠送余额）
     */
    private Integer usedTotalUsedScore =0;

}
