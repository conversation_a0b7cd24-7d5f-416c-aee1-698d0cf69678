package com.ly.titc.pms.member.asset.dal.entity.dto;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 16:58
 */
@Data
@Accessors(chain = true)
public class RechargeWrapper {

    /**
     * 新增记录
     */
    private MemberStoreRechargeRecord addRecord;

    /**
     * 修改记录
     */
    private MemberStoreRechargeRecord updateRecord;

    private Integer version;

    /**
     * 储值总额 变更本金金额
     */
    private BigDecimal capitalAmount = new BigDecimal(0);

    /**
     * 变动礼金金额
     */
    private BigDecimal giftAmount = new BigDecimal(0);

    /**
     * 会员编号
     */
    private String memberNo;
}
