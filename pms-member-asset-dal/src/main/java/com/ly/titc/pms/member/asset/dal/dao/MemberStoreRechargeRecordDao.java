package com.ly.titc.pms.member.asset.dal.dao;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员储值充值记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface MemberStoreRechargeRecordDao extends BaseMapper<MemberStoreRechargeRecord> {

   int  batchUpdateByBalanceWithLock(@Param("records") List<MemberStoreRechargeRecord> records, @Param("memberNo") String memberNo);

}
