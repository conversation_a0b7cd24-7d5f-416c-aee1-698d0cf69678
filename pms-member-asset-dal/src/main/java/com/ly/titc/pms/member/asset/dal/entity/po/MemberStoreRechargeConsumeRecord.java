package com.ly.titc.pms.member.asset.dal.entity.po;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 会员储值消费记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberStoreRechargeConsumeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 消费主体类型  1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 消费主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 消费记录号
     */
    private String consumeRecordNo;

    /**
     * 原消费记录号(交易类型为退款时)
     */
    private String originalConsumeRecordNo;

    /**
     * 消费类型：pay:支付，freeze冻结, refund 退款
     */
    private String consumeType;

    /**
     * 消费描述（内容） 冻结为冻结原因
     */
    private String consumeDesc;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 已退本金
     */
    private BigDecimal refundCapitalAmount;

    /**
     * 已退礼金
     */
    private BigDecimal refundGiftAmount;

    /**
     * 已退总金额
     */
    private BigDecimal refundTotalAmount;


    /**
     * 收银场景
     */
    private String cashierScene;

    /**
     * 业务消费号（订单号）(来源系统+业务消费号幂等)
     */
    private String bizConsumeNo;


    /**
     * 来源系统：会员-MEMBER，商品部-SHOP 餐饮-FOOD 微订房-WEBOOKNG 收银台——CASHIER
     */
    private String sourceSystem;



//    /**
//     * 使用场景
//     */
//    private String scene;

//    /**
//     * 业务线唯一号
//     */
//    private String businessNo;
//
//    /**
//     *  WXBOOKINGPAY(微订房支付) ,PMSPAY(PMS支付)
//     */
//    private String businessType;

    /**
     * 业务描述
     */
    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createTime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updateTime;


}
