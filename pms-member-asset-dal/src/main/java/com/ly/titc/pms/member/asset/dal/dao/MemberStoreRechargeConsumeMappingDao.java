package com.ly.titc.pms.member.asset.dal.dao;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 会员储值消费记录mapping Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface MemberStoreRechargeConsumeMappingDao extends BaseMapper<MemberStoreRechargeConsumeMapping> {

    int batchUpdateByBalanceWithLock(List<MemberStoreRechargeConsumeMapping> records, String memberNo);
}
