package com.ly.titc.pms.member.asset.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员积分获取记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberPointReceiveRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 记录号
     */
    private String receiveRecordNo;

    /**
     * 积分主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 积分主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    private String platformChannel;

    /**
     * 交易类型 RECEIVE:获得 RECOVERY:回收
     */
    private String receiveType;

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP,营销活动 SPM
     */
    private String businessType;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 操作类型 ADJUST(调整)，GIVE:事件赠送 RECOVERY:事件赠送回收 
     */
    private String actionType;

    /**
     * 积分项目：日结加积分、日租房加积分、预定担保扣积分、积分兑换...
     */
    private String actionItem;

    /**
     * 积分项目描述
     */
    private String actionItemDesc;

    /**
     * 积分数
     */
    private Integer score;

    /**
     * 剩余积分(积分过期时使用剩余积分过期)
     */
    private Integer balanceScore;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 备注（原因）
     */
    private String remark;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 原获得记录号（回滚时有）
     */
    private String originalConsumeRecordNo;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createtime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updatetime;


}
