package com.ly.titc.pms.member.asset.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会员积分消费记录mapping
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberPointReceiveConsumeMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 消费主体类型  1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 消费主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 消费类型：PAY:支付，REFUND 退款 ADJUST(调整)
     */
    private String consumeType;

    /**
     * 消费记录号
     */
    private String consumeRecordNo;

    /**
     * 充值记录号
     */
    private String receiveRecordNo;

    /**
     * 原记录号（退款时有）
     */
    private String originalConsumeRecordNo;

    /**
     * 积分数
     */
    private Integer score;

    // todo 是否支持部分退
//    /**
//     * 剩余积分数 （部分退需要记录）
//     */
//    private Integer remainScore;
    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 是否是回滚退款
     */
    private Integer isRollback;

    /**
     * 版本号
     */
//    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;

    /**
     * 创建时间;dbRouter内建字段	
     */
    @TableField("CreateTime")
    private Timestamp createtime;

    /**
     * 更新时间;dbRouter内建字段
     */
    @TableField("UpdateTime")
    private Timestamp updatetime;


}
