<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberPointReceiveRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="receive_record_no" property="receiveRecordNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="platform_channel" property="platformChannel" />
        <result column="receive_type" property="receiveType" />
        <result column="business_type" property="businessType" />
        <result column="business_no" property="businessNo" />
        <result column="action_type" property="actionType" />
        <result column="action_item" property="actionItem" />
        <result column="action_item_desc" property="actionItemDesc" />
        <result column="score" property="score" />
        <result column="balance_score" property="balanceScore" />
        <result column="activity_code" property="activityCode" />
        <result column="expire_date" property="expireDate" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
        <result column="original_consume_record_no" property="originalConsumeRecordNo" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, receive_record_no, master_type, master_code, club_code, bloc_code, hotel_code, platform_channel, receive_type, business_type, business_no, action_type, action_item, action_item_desc, score, balance_score, activity_code, expire_date, remark, version, original_consume_record_no, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>

</mapper>
