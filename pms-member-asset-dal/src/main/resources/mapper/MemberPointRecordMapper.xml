<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberPointRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="record_no" property="recordNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="platform_channel" property="platformChannel" />
        <result column="action_type" property="actionType" />
        <result column="action_no" property="actionNo" />
        <result column="trade_type" property="tradeType" />
        <result column="original_business_no" property="originalBusinessNo" />
        <result column="action_item" property="actionItem" />
        <result column="action_item_desc" property="actionItemDesc" />
        <result column="score" property="score" />
        <result column="balance_score" property="balanceScore" />
        <result column="activity_code" property="activityCode" />
        <result column="expire_date" property="expireDate" />
        <result column="scene" property="scene" />
        <result column="business_type" property="businessType" />
        <result column="business_no" property="businessNo" />
        <result column="business_note" property="businessNote" />
        <result column="remark" property="remark" />
        <result column="version" property="version" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, record_no, master_type, master_code, club_code, bloc_code, hotel_code, platform_channel, action_type, action_no, trade_type, original_business_no, action_item, action_item_desc, score, balance_score, activity_code, expire_date, scene, business_type, business_no, business_note, remark, version, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>
    <select id="getMasterTotalUsedPoint"
            resultType="com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedPointDto">
        SELECT
        master_type as masterType,
        master_code as masterCode,
        IFNULL(SUM( score ),0) AS usedTotalUsedScore
        FROM
        member_point_record
        WHERE
        member_no = #{memberNo}
        AND master_type = #{masterType}
        AND master_code = #{masterCode}
        <if test="tradeTypes !=null and tradeTypes.size >0">
            AND trade_type in
            <foreach collection="tradeTypes" item="tradeType" open="(" separator="," close=")">
                #{tradeType}
            </foreach>
        </if>
        GROUP BY
        master_type,
        master_code

    </select>

</mapper>
