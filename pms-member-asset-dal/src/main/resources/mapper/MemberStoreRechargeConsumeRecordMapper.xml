<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeConsumeRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="consume_record_no" property="consumeRecordNo" />
        <result column="original_consume_record_no" property="originalConsumeRecordNo" />
        <result column="consume_type" property="consumeType" />
        <result column="consume_desc" property="consumeDesc" />
        <result column="platform_channel" property="platformChannel" />
        <result column="capital_amount" property="capitalAmount" />
        <result column="gift_amount" property="giftAmount" />
        <result column="total_amount" property="totalAmount" />
        <result column="scene" property="scene" />
        <result column="business_no" property="businessNo" />
        <result column="business_type" property="businessType" />
        <result column="business_note" property="businessNote" />
        <result column="remark" property="remark" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, master_type, master_code, club_code, bloc_code, hotel_code, consume_record_no, original_consume_record_no, consume_type, consume_desc, platform_channel, capital_amount, gift_amount, total_amount, scene, business_no, business_type, business_note, remark, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>

    <select id="getMasterTotalUsedAmount" resultType="com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto">
        SELECT
            master_type as masterType,
            master_code as masterCode,
            IFNULL(SUM( capital_amount ),0) AS usedTotalCapitalAmount,
            IFNULL(SUM( gift_amount ),0) AS usedTotalGiftAmount,
            IFNULL(SUM( total_amount ),0)AS usedTotalAmount

        FROM
            member_store_recharge_consume_record
        WHERE
           member_no = #{memberNo}
          AND master_type = #{masterType}
          AND master_code = #{masterCode}
        <if test="consumeTypes !=null and consumeTypes.size >0">
            AND consume_type in
            <foreach collection="consumeTypes" item="consumeType" open="(" separator="," close=")">
                #{consumeType}
            </foreach>
        </if>
        GROUP BY
            master_type,
            master_code
    </select>


</mapper>
