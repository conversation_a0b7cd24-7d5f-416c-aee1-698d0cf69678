<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="recharge_record_no" property="rechargeRecordNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="trade_type" property="tradeType" />
        <result column="trade_no" property="tradeNo" />
        <result column="platform_channel" property="platformChannel" />
        <result column="capital_amount" property="capitalAmount" />
        <result column="gift_amount" property="giftAmount" />
        <result column="total_amount" property="totalAmount" />
        <result column="capital_balance" property="capitalBalance" />
        <result column="gift_balance" property="giftBalance" />
        <result column="total_balance" property="totalBalance" />
        <result column="activity_code" property="activityCode" />
        <result column="gift_expire_date" property="giftExpireDate" />
        <result column="usage_rule_mode" property="usageRuleMode" />
        <result column="usage_rule_id" property="usageRuleId" />
        <result column="version" property="version" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, recharge_record_no, master_type, master_code, club_code, bloc_code, hotel_code, trade_type, trade_no, platform_channel, capital_amount, gift_amount, total_amount, capital_balance, gift_balance, total_balance, activity_code, gift_expire_date, usage_rule_mode, usage_rule_id, version, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>


    <update id="batchUpdateByBalanceWithLock">
        UPDATE member_store_recharge_record
        SET
        capital_balance = CASE recharge_record_no
        <foreach collection="records" item="record">
            WHEN #{record.rechargeRecordNo} THEN #{record.capitalBalance}
        </foreach>
        END,
        gift_balance = CASE recharge_record_no
        <foreach collection="records" item="record">
            WHEN #{record.rechargeRecordNo} THEN #{record.giftBalance}
        </foreach>
        END,
        total_balance = CASE recharge_record_no
        <foreach collection="records" item="record">
            WHEN #{record.rechargeRecordNo} THEN #{record.totalBalance}
        </foreach>
        END,
        modify_user = CASE recharge_record_no
        <foreach collection="records" item="record">
            WHEN #{record.rechargeRecordNo} THEN #{record.modifyUser}
        </foreach>
        END,
        version = version + 1
        WHERE
        member_no = #{memberNo}  <!-- 固定 memberNo -->
        AND (recharge_record_no, version) IN
        <foreach collection="records" item="record" open="(" close=")" separator=",">
            (#{record.rechargeRecordNo}, #{record.version})
        </foreach>
    </update>



</mapper>
