<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeInvalidRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeInvalidRecord">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="recharge_record_no" property="rechargeRecordNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="asset_type" property="assetType" />
        <result column="invalid_type" property="invalidType" />
        <result column="amount" property="amount" />
        <result column="score" property="score" />
        <result column="invalid_date" property="invalidDate" />
        <result column="reason" property="reason" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, recharge_record_no, master_type, master_code, club_code, bloc_code, hotel_code, asset_type, invalid_type, amount, score, invalid_date, reason, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>

</mapper>
