<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberStoreAccountInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreAccountInfo">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="total_amount" property="totalAmount" />
        <result column="total_capital_amount" property="totalCapitalAmount" />
        <result column="total_gift_amount" property="totalGiftAmount" />
        <result column="total_balance" property="totalBalance" />
        <result column="total_capital_balance" property="totalCapitalBalance" />
        <result column="total_gift_balance" property="totalGiftBalance" />
        <result column="total_expire_gift_amount" property="totalExpireGiftAmount" />
        <result column="total_used_amount" property="totalUsedAmount" />
        <result column="total_used_capital_amount" property="totalUsedCapitalAmount" />
        <result column="total_used_gift_amount" property="totalUsedGiftAmount" />
        <result column="total_frozen_amount" property="totalFrozenAmount" />
        <result column="total_frozen_capital_amount" property="totalFrozenCapitalAmount" />
        <result column="total_frozen_gift_amount" property="totalFrozenGiftAmount" />
        <result column="version" property="version" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, master_type, master_code, club_code, bloc_code, hotel_code, total_amount, total_capital_amount, total_gift_amount, total_balance, total_capital_balance, total_gift_balance, total_expire_gift_amount, total_used_amount, total_used_capital_amount, total_used_gift_amount, total_frozen_amount, total_frozen_capital_amount, total_frozen_gift_amount, version, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>

</mapper>
