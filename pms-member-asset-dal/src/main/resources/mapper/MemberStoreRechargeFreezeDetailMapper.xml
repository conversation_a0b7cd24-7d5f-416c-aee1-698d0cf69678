<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberStoreRechargeFreezeDetailDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeFreezeDetail">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="consume_record_no" property="consumeRecordNo" />
        <result column="freeze_date" property="freezeDate" />
        <result column="is_freeze_long" property="isFreezeLong" />
        <result column="unfreeze_user" property="unfreezeUser" />
        <result column="unfreeze_date" property="unfreezeDate" />
        <result column="unfreeze_reason" property="unfreezeReason" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, master_type, master_code, club_code, bloc_code, hotel_code, consume_record_no, freeze_date, is_freeze_long, unfreeze_user, unfreeze_date, unfreeze_reason, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>

</mapper>
