<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.titc.pms.member.asset.dal.dao.MemberPointAccountInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo">
        <id column="id" property="id" />
        <result column="member_no" property="memberNo" />
        <result column="master_type" property="masterType" />
        <result column="master_code" property="masterCode" />
        <result column="club_code" property="clubCode" />
        <result column="bloc_code" property="blocCode" />
        <result column="hotel_code" property="hotelCode" />
        <result column="total_score" property="totalScore" />
        <result column="total_score_balance" property="totalScoreBalance" />
        <result column="total_expire_score" property="totalExpireScore" />
        <result column="total_used_score" property="totalUsedScore" />
        <result column="version" property="version" />
        <result column="create_user" property="createUser" />
        <result column="modify_user" property="modifyUser" />
        <result column="is_delete" property="isDelete" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="CreateTime" property="createtime" />
        <result column="UpdateTime" property="updatetime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_no, master_type, master_code, club_code, bloc_code, hotel_code, total_score, total_score_balance, total_expire_score, total_used_score, version, create_user, modify_user, is_delete, gmt_create, gmt_modified, CreateTime, UpdateTime
    </sql>

</mapper>
