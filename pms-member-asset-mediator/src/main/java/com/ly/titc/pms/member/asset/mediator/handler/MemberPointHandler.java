package com.ly.titc.pms.member.asset.mediator.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleAssetResp;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberStoreUsageModeEnum;
import com.ly.titc.pms.member.asset.biz.MemberPointReceiveRecordBiz;
import com.ly.titc.pms.member.asset.com.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.enums.StateEnum;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedPointDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.mediator.convert.MemberPointMedConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberMasterUsableScoresDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberPointBalanceDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberPointUsageRuleDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import com.ly.titc.pms.member.asset.mediator.rpc.dubbo.ecrm.MemberPointUsageDecorator;
import com.ly.titc.pms.member.asset.service.MemberPointRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 积分处理器
 * @since 2024-11-14 11:58
 */
@Slf4j
@Component
public class MemberPointHandler {

    @Resource
    private MemberPointRecordService pointRecordService;

    @Resource
    private MemberPointMedConverter pointMedConverter;
    @Resource
    private MemberStoreExpireHandler expireHandler;
    @Resource
    private MemberPointReceiveRecordBiz receiveRecordBiz;
    @Resource
    private MemberPointUsageDecorator usageDecorator;




    /**
     * 计算总余额和可用余额
     * @return
     */
    public MemberMasterUsableScoresDto calculateBalancePoint(GetMemberUsableDto memberDto) {
        MemberMasterUsableScoresDto pointBalanceDto = new MemberMasterUsableScoresDto();
        //1.查询会员储值记录
        String memberNo = memberDto.getMemberNo();
        List<MemberPointReceiveRecord> records = receiveRecordBiz.listByBalanceGtZero(memberNo);
        //2.计算总余额
        MemberPointBalanceDto balanceDto = calculateRecordBalance(records);
        pointBalanceDto.setTotalScoreBalance(balanceDto.getTotalScoreBalance());
        //3.计算可用余额
        if(memberDto.getIsNeedUsage()){
            //3.1.查询该主体所有的使用规则
            List<MemberPointUsageRuleAssetResp> ruleAssetResps =  usageDecorator.listRuleForAsset(memberDto.getMasterType(),memberDto.getMasterCode(),memberDto.getPlatformChannel());
            List<MemberPointUsageRuleDto> usageRuleDtoList = pointMedConverter.convert(ruleAssetResps);
            //3.2.计算可用余额
            MemberMasterUsableScoresDto usableBalanceInfoDto = calculateUsableSingleRule(records,usageRuleDtoList,memberDto);
            pointBalanceDto.setUsableTotalScore(usableBalanceInfoDto.getUsableTotalScore());
            pointBalanceDto.setUsableRecords(usableBalanceInfoDto.getUsableRecords());
        }
        return pointBalanceDto;
    }

    /**
     * 计算总消费金额
     * @param memberDto
     * @return
     */
    public MemberUsedPointDto calculateUsedPoint(GetMemberUsableDto memberDto){
        List<String> tradeTypes = new ArrayList<>();
        tradeTypes.add(AssetConsumeTypeEnum.PAY.getCode());
        tradeTypes.add(AssetConsumeTypeEnum.REFUND.getCode());
        return pointRecordService.getMasterTotalUsedPoint(memberDto.getMemberNo(),memberDto.getMasterType(),memberDto.getMasterCode(),tradeTypes);
    }




    /**
     * 计算总余额
     * @param records
     * @return
     */
    private MemberPointBalanceDto calculateRecordBalance(List<MemberPointReceiveRecord> records) {
        Integer totalScoreBalance = 0;
        MemberPointBalanceDto balanceDto = new MemberPointBalanceDto();
        balanceDto.setTotalScoreBalance(totalScoreBalance);
        if(CollectionUtils.isEmpty(records)){
            return balanceDto;
        }
        List<MemberPointReceiveRecord> expiredGiftRecords = new ArrayList<>();

        for (MemberPointReceiveRecord record : records) {
            //判断是否过期
            if (StringUtils.isNotEmpty(record.getExpireDate() )&& !LocalDateUtil.parseByNormalDate(record.getExpireDate()).isBefore(LocalDateUtil.now())) {
                if(record.getBalanceScore() > 0){
                    totalScoreBalance = totalScoreBalance+(record.getBalanceScore());

                }
            }else {
                expiredGiftRecords.add(record);
            }
        }
        //过期处理
        expireHandler.expireMemberPoint(expiredGiftRecords);
        balanceDto.setTotalScoreBalance(totalScoreBalance);
        return balanceDto;
    }


    /**
     * 计算可用余额
     */
    private MemberMasterUsableScoresDto calculateUsableSingleRule(List<MemberPointReceiveRecord> records, List<MemberPointUsageRuleDto> singleRuleList, GetMemberUsableDto memberDto) {
        if(CollectionUtils.isEmpty(singleRuleList)){
            log.warn("该会员的积分使用方没有配置使用规则，无可用余额：{}", JSON.toJSONString(memberDto));
            throw new ServiceException(RespCodeEnum.CODE_2004);
        }
        //过滤使用模式为单一使用规则模式
        if(singleRuleList.size()>1){
            log.error("该会员的积分使用方配置了多个单一使用规则，无法计算可用余额：{}", JSON.toJSONString(memberDto));
            throw new ServiceException(RespCodeEnum.CODE_2002);
        }
        MemberPointUsageRuleDto singleRule = singleRuleList.get(0);
        //不可用使用积分
        if(singleRule.getIsCanUse().equals(StateEnum.CLOSE.getState())){
            log.warn("该会员的积分使用方没有配置使用规则，无可用余额：{}", JSON.toJSONString(memberDto));
            throw new ServiceException(RespCodeEnum.CODE_2005);

        }
        List<MemberPointReceiveRecord> matchRecords = new ArrayList<>();
//        List<MemberPointRecordRuleRecordDto> pointRecordRules = new ArrayList<>();
        //匹配使用规则
        records.forEach(record -> {
            //单一模式需要根据使用方和平台适用范围匹配
            //全部平台可用或者部分可用包含此平台
            if(!matchPlatformChannelScope(singleRule,record)){
                return;
            }
            //判断储值使用模式
            MemberPointReceiveRecord matchRecord = matchUsageMode(record, singleRule, memberDto);
            if(matchRecord != null){
                matchRecords.add(matchRecord);
//                MemberPointRecordRuleRecordDto recordDto = pointMedConverter.convertRecordDto(record);
//                pointRecordRules.add(recordDto);
            }

        });

        MemberMasterUsableScoresDto usableBalanceDto = new MemberMasterUsableScoresDto();
        //计算可用可用额度
        MemberPointBalanceDto balanceDto=   calculateRecordBalance(matchRecords);
        usableBalanceDto.setUsableTotalScore(balanceDto.getTotalScoreBalance());
        usableBalanceDto.setUsableRecords(matchRecords);
        return usableBalanceDto;
    }

    public boolean matchPlatformChannelScope(MemberPointUsageRuleDto usageRuleDto, MemberPointReceiveRecord record){
        List<String> platformScopeValues = usageRuleDto.getScopePlatformChannels();
        if(CollectionUtils.isEmpty(platformScopeValues)){
            return false;
        }
        return platformScopeValues.contains(record.getPlatformChannel());
    }

    public MemberPointReceiveRecord matchUsageMode(MemberPointReceiveRecord record, MemberPointUsageRuleDto usageRuleDto, GetMemberUsableDto memberDto){
        Integer usageMode = usageRuleDto.getUsageMode();
        MemberStoreUsageModeEnum usageModeEnum = MemberStoreUsageModeEnum.getByValue(usageMode);
        if(usageModeEnum == null){
            log.error("使用规则中的使用模式不存在：{}", JSON.toJSONString(usageRuleDto));
            throw new ServiceException(RespCodeEnum.CODE_1014);
        }
        Integer masterType = memberDto.getMasterType();
        String masterCode = memberDto.getMasterCode();
        switch (usageModeEnum){
            case ALONE:
                //仅充值门店可用
                if(record.getMasterType().equals(masterType) && record.getMasterCode().equals(masterCode)){
                    return record;
                }
                break;
            case ALL:
                //全部门店可用 todo 全部门店可用时需要区分集团和集团组的吗
                return record;
            case ALLOCATE:
                //指定门店可用
                List<MasterObject> masterObjects =  usageRuleDto.getUsageModeScopes();
                for (MasterObject masterObject : masterObjects) {
                    if(masterObject.getMasterType().equals(masterType) && masterObject.getMasterCode().equals(masterCode)){
                        return record;
                    }
                }
                break;
        }
        return null;
    }
}
