package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
@Accessors(chain = true)
public class MemberStoreBalanceContext {

    // 输入参数
    private GetMemberUsableDto memberDto;

    //会员号
    private String memberNo;

    // 查询结果
    private List<MemberStoreRechargeRecord> rechargeRecords;
    private List<MemberStoreRechargeRuleRecordDto> matchedRuleRecords;

    // 余额计算结果
    private MemberBalanceDto totalBalanceDto;
    private MemberBalanceDto usableBalanceDto;

    // 规则相关
    private List<MemberStoreRechargeRecord> matchedRecords;
//    private Map<String, List<MemberStoreUsageRuleDto>> ruleTypeRecordsMap;

    private Map<String, List<MemberStoreUsageRuleDto>> singleRulesMap;
    private Map<String, List<MemberStoreUsageRuleDto>> multiRulesMap;


    //过期记录
    private List<MemberStoreRechargeRecord> expiredGiftRecords;




    // 构造函数
    public MemberStoreBalanceContext(GetMemberUsableDto memberDto) {
        this.memberDto = memberDto;
        this.rechargeRecords = new ArrayList<>();
        this.matchedRecords = new ArrayList<>();
        this.matchedRuleRecords = new ArrayList<>();
        this.singleRulesMap = new HashMap<>();
        this.multiRulesMap =new HashMap<>();
        this.memberNo = memberDto.getMemberNo();
    }


    public void addMatchedRecord(MemberStoreRechargeRecord record) {
        this.matchedRecords.add(record);
    }

    public void addMatchedRuleRecord(MemberStoreRechargeRuleRecordDto ruleRecord) {
        this.matchedRuleRecords.add(ruleRecord);
    }

}
