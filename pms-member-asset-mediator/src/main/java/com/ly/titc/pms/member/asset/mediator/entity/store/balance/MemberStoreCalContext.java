package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-19 16:44
 */
@Data
@Accessors(chain = true)
public class MemberStoreCalContext {
    /**
     * 本金余额
     */
    private List<MemberStoreRechargeRecordBalanceDto> capitalBalances;

    /**
     * 礼金余额
     */
    private List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances;

    public MemberStoreCalContext() {
        this.capitalBalances = new ArrayList<>();
        this.giftBalances = new ArrayList<>();
    }

}
