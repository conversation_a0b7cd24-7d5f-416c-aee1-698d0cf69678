package com.ly.titc.pms.member.asset.mediator.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeFreezeDetailBiz;
import com.ly.titc.pms.member.asset.com.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeFreezeDetail;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.RechargeTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.TradeOpetatorTypeEnum;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.store.*;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreConsumeHandler;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreHandler;
import com.ly.titc.pms.member.asset.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoreRechargeConsumeMedService;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeMappingService;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeRecordService;
import com.ly.titc.pms.member.asset.service.MemberStoreRecordService;
import com.ly.titc.springboot.redisson.client.RedissonLockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:07
 */
@Slf4j
@Service
public class MemberStoreRechargeConsumeMedServiceImpl implements MemberStoreRechargeConsumeMedService {

    @Resource
    private MemberStoreConverter storeConverter;

    @Resource
    private MemberStoreRechargeConsumeRecordService consumeRecordService;

    @Resource
    private MemberStoreRechargeFreezeDetailBiz freezeDetailService;

    @Resource
    private MemberStoreRecordService memberStoreRecordService;

    @Resource
    private HotelDecorator hotelDecorator;


    @Override
    public MemberStoreConsumeRecordDto listByBusinessNo(ListMemberStoreConsumeByBusinessNoDto dto) {
        return null;
    }

    @Override
    public Pageable<MemberStoreConsumeRecordDto> pageConsumeRecord(PageMemberStoreConsumeDto dto) {

        Page<MemberStoreRechargeConsumeRecord> page = consumeRecordService.page(dto.getMemberNo(), dto.getMasterCode(), dto.getMasterType(), dto.getPlatformChannel(), dto.getBeginTime(), dto.getEndTime(), dto.getPageIndex(), dto.getPageSize(), dto.getConsumeType());
        List<MemberStoreRechargeConsumeRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        List<String> hotelCodes = records.stream().filter(item -> item.getMasterType().equals(MasterTypeEnum.HOTEL.getType()))
                .map(MemberStoreRechargeConsumeRecord::getMasterCode).distinct().collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(hotelCodes);
        Map<String, String> hotelNameMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName));

        if (dto.getConsumeType().equalsIgnoreCase(AssetConsumeTypeEnum.FREEZE.getCode())) {
            // 查询冻结详情拼接返回
            List<String> consumeRecordNo = records.stream().map(MemberStoreRechargeConsumeRecord::getConsumeRecordNo).collect(Collectors.toList());
            List<MemberStoreRechargeFreezeDetail> detailList = freezeDetailService.listByConsumeRecordNo(consumeRecordNo, dto.getMemberNo());
            return PageableUtil.convert(page, storeConverter.convertConsumeRecordDto(records, detailList, hotelNameMap));
        }

        List<MemberStoreConsumeRecordDto> dtoList = records.stream()
                .map(record -> storeConverter.convertConsumeRecordDtoItem(record, hotelNameMap.getOrDefault(record.getMasterCode(), "集团")))
                .collect(Collectors.toList());
        return PageableUtil.convert(page, dtoList);
    }

    @Override
    public List<MemberTradeConsumeRecordDto> listRechargeConsumeRecord(String memberNo, List<String> tradeNoList) {

        List<MemberTradeConsumeRecordDto> resultList = new ArrayList<>();

        List<MemberStoreRechargeRecord> records = memberStoreRecordService.getByTradeNo(tradeNoList, memberNo, RechargeTypeEnum.RECHARGE.getType());
        // 根据交易号分组， 并获得交易号对应的消费记录号列表
        Map<String, MemberStoreRechargeRecord> tradeNoMap = records.stream().collect(Collectors.toMap(MemberStoreRechargeRecord::getTradeNo, record -> record));
//        List<String> rechargeRecordNoList = records.stream().map(MemberStoreRechargeRecord::getRechargeRecordNo)
//                .collect(Collectors.toList());
        // 查询消费mapping
//        List<MemberStoreRechargeConsumeMapping> consumeRecords = memberStoreConsumeMappingService.listByRechargeRecordNoList(rechargeRecordNoList);

        tradeNoMap.forEach((k, v) -> {
//            List<MemberStoreRechargeConsumeMapping> itemConsumeRecords = consumeRecords.stream().filter(item -> v.getRechargeRecordNo().equals(item.getRechargeRecordNo()) && item.getConsumeType().equals(AssetConsumeTypeEnum.PAY.getCode()))
//                    .collect(Collectors.toList());
//            List<MemberStoreRechargeConsumeMapping> itemRefundRecords = consumeRecords.stream().filter(item -> v.getRechargeRecordNo().equals(item.getRechargeRecordNo()) && item.getConsumeType().equals(AssetConsumeTypeEnum.REFUND.getCode()))
//                    .collect(Collectors.toList());
            MemberTradeConsumeRecordDto consumeRecordDto = new MemberTradeConsumeRecordDto();
            consumeRecordDto.setTradeNo(k);
            consumeRecordDto.setConsumeAmount(v.getCapitalAmount().subtract(v.getCapitalBalance()));
            consumeRecordDto.setConsumeGiftAmount(v.getGiftAmount().subtract(v.getGiftBalance()));
            consumeRecordDto.setCapitalAmount(v.getCapitalAmount());
            consumeRecordDto.setGiftAmount(v.getGiftAmount());
            resultList.add(consumeRecordDto);
        });

        return resultList;
    }
}
