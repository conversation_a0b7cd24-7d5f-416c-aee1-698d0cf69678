package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:55
 */
@Data
@Accessors
public class MemberStoreRechargeDto extends BaseMemberDto {


    /**
     * @see com.ly.titc.pms.member.asset.dubbo.enums.RechargeTypeEnum
     */
    private String tradeType;

    /**
     * 订单交易号（充值订单号，退款订单号）
     */
    private String tradeNo;

    /**
     * 原始充值交易号（退款时必传）
     */
    private String originalTradeNo;


    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 赠送礼金的有效期
     * yyyy-MM-dd
     */
    private String giftExpireDate;

    /**
     * 充值活动code
     */
    private String activityCode;

    /**
     * 使用规则模式
     */
    private String usageRuleMode;
    /**
     * 使用规则Code
     * (多规则模式必传)
     */
    private String usageRuleId;
    /**
     * 备注
     */
    private String remark;
}
