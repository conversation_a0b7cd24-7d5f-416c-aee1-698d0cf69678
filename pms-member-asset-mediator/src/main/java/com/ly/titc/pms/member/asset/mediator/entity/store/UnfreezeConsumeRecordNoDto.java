package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-3 14:02
 */
@Data
@Accessors(chain = true)
public class UnfreezeConsumeRecordNoDto extends BaseMemberDto {

    /**
     * 消费记录号
     */
    private String consumeRecordNo;

    /**
     * 解冻原因
     */
    private String unfreezeReason;

    /**
     * 操作人
     */
    private String operator;

}
