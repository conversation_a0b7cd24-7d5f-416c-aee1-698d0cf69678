package com.ly.titc.pms.member.asset.mediator.entity.store;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-26 19:10
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberStoreConsumeCalDto {

    /**
     * Success 成功
     * Fail 系统异常失败
     * Insufficient_Balance 余额不足 :本次支付至少需要XX储值，当前会员储值余额不足，请充值或更换支付
     * Rule_UnMatch 规则不匹配 :当前场景无法使用储值，请维护储值使用规则
     */
    private String result;

    /**
     * 使用储值本金
     */
    private BigDecimal usedCapitalAmount= BigDecimal.ZERO;

    /**
     * 使用储值礼金
     */
    private BigDecimal usedGiftAmount = BigDecimal.ZERO;

    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal usableTotalBalance = BigDecimal.ZERO;

    /**
     * 总本金余额
     */
    private BigDecimal usableTotalCapitalBalance = BigDecimal.ZERO;

    /**
     * 总赠送余额
     */
    private BigDecimal usableTotalGiftBalance = BigDecimal.ZERO;

    public MemberStoreConsumeCalDto(String result) {
        this.result = result;
    }
}
