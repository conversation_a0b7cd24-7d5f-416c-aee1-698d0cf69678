package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class GetMemberTotalBalanceMemberDto extends BaseMemberDto {
    /**
     * 主体类型
     */
    private Integer masterType;

    /**
     * 主体code
     */
    private String masterCode;

}
