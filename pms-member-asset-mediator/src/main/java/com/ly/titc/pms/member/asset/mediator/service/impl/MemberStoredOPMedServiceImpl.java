package com.ly.titc.pms.member.asset.mediator.service.impl;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeFreezeDetailBiz;
import com.ly.titc.pms.member.asset.com.constant.SystemConstant;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.utils.CommonUtil;
import com.ly.titc.pms.member.asset.dal.entity.dto.RechargeWrapper;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeFreezeDetail;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.dubbo.enums.RechargeTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.StoreUseResultEnum;
import com.ly.titc.pms.member.asset.entity.MemberStoreConsumeWrapper;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.*;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import com.ly.titc.pms.member.asset.mediator.handler.AssetCheckHandler;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreConsumeHandler;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreHandler;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreRefundHandler;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoredOPMedService;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeRecordService;
import com.ly.titc.pms.member.asset.service.MemberStoreRecordService;
import com.ly.titc.springboot.redisson.client.RedissonLockClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:52
 */
@Slf4j
@Service
public class MemberStoredOPMedServiceImpl implements MemberStoredOPMedService {
    @Resource
    private MemberStoreConverter storeConverter;
    @Resource
    private MemberStoreRecordService memberStoreRecordService;
    @Resource
    private MemberStoreHandler memberStoreHandler;

    @Resource(type = RedissonLockClient.class)
    private RedissonLockClient redissonLockClient;
    @Resource
    private AssetCheckHandler assetCheckHandler;

    @Resource
    private MemberStoreConsumeHandler consumeHandler;

    @Resource
    private MemberStoreRechargeConsumeRecordService consumeRecordService;
    @Resource
    private MemberStoreRechargeFreezeDetailBiz freezeDetailBiz;
    @Resource
    private MemberStoreRefundHandler refundHandler;

    @Override
    public MemberStoreRecordOPResultDto recharge(MemberStoreRechargeDto dto) {
        //1.校验参数
        assetCheckHandler.checkArgs(dto);
        if(dto.getGiftAmount().compareTo(BigDecimal.ZERO) > 0 && StringUtils.isEmpty(dto.getGiftExpireDate())){
            throw new ServiceException("赠送金额有效期不能为空", RespCodeEnum.CODE_400.getCode());
        }
        //2.保存交易记录
        String lockKey = SystemConstant.RECHARGE_LOCK+dto.getTradeNo();
        RLock rLock = redissonLockClient.lock(lockKey, 0L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getTradeNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "储值余额处理中，请稍后重试");
        }
        MemberStoreRechargeRecord record = null;
        try {
            //校验幂等
            MemberStoreRechargeRecord existRecord = memberStoreRecordService.getByTradeNo(dto.getTradeNo(), dto.getMemberNo(),RechargeTypeEnum.RECHARGE.getType());
            if (existRecord != null) {
                log.error("交易号已存在{}", dto.getTradeNo());
                throw new ServiceException(RespCodeEnum.CODE_1001);
            }
            RechargeWrapper wrapper = new RechargeWrapper();
            record = storeConverter.convert(dto, CommonUtil.generateUniqueNo(SystemConstant.RECHARGE_NO_PREFIX));
            wrapper.setCapitalAmount(dto.getCapitalAmount());
            wrapper.setGiftAmount(dto.getGiftAmount());
            wrapper.setAddRecord(record);
            wrapper.setMemberNo(dto.getMemberNo());
            memberStoreRecordService.saveRecords(wrapper);

        }finally {
            rLock.unlock();
        }
        return storeConverter.convert(record);
    }

    @Override
    public MemberStoreRecordOPResultDto rechargeRollback(MemberStoreRechargeRollBackDto dto) {

        //1.校验参数
        assetCheckHandler.checkArgs(dto);
        //2.保存交易记录
        String lockKey = SystemConstant.RECHARGE_LOCK+dto.getTradeNo();
        RLock rLock = redissonLockClient.lock(lockKey, 0L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getTradeNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "储值余额处理中，请稍后重试");
        }
        //查询原交易单
        MemberStoreRechargeRecord originalRecord = memberStoreRecordService.getByTradeNo(dto.getTradeNo(), dto.getMemberNo(),RechargeTypeEnum.RECHARGE.getType());
        if(originalRecord == null){
            log.error("原交易单不存在{}", dto.getTradeNo());
            throw new ServiceException(RespCodeEnum.CODE_1002);
        }
        //查询是否已退款
        MemberStoreRechargeRecord refundRecord= memberStoreRecordService.getByTradeNo(dto.getTradeNo(),dto.getMemberNo(),RechargeTypeEnum.RECHARGE_REFUND.getType());
        if(refundRecord !=null){
            log.error("已退款不支持重复退:{}",dto.getTradeNo());
            throw new ServiceException(RespCodeEnum.CODE_1008);
        }
        RechargeWrapper wrapper = storeConverter.convert(originalRecord,dto);
        memberStoreRecordService.saveRecords(wrapper);

        return storeConverter.convert(wrapper.getUpdateRecord());
    }

    @Override
    public MemberStoreRecordOPResultDto consumeStore(MemberMemberStoreConsumeDto dto) {
        //2.获取消费锁，锁的维度为会员编号
        String lockKey = SystemConstant.RECHARGE_CONSUME_LOCK+dto.getMemberNo();
        RLock rLock = redissonLockClient.lock(lockKey, 3L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getMemberNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "储值余额处理中，请稍后重试");
        }
        try{
            //校验是否重复消费
            MemberStoreRechargeConsumeRecord consumeRecord = consumeRecordService.getByBizConsumeNo(dto.getMemberNo(),dto.getBizConsumeNo(),dto.getSourceSystem());
            if(consumeRecord != null){
                log.error("已消费不支持重复消费:{}",dto.getBizConsumeNo());
                throw new ServiceException("请勿重复发起消费",RespCodeEnum.CODE_400.getCode());
            }
            //消费金额
            BigDecimal amount = dto.getAmount();
            //查询储值的可用余额
            MemberStoreBalanceResult result =  memberStoreHandler.calculateBalance(storeConverter.convert(dto));
            if(!result.isSuccess()){
                throw new ServiceException(RespCodeEnum.Store_UnMatch_Error);
            }
            MemberMasterUsableBalanceDto usableBalanceDto  = result.getUsableBalanceDto();
            //1.校验可用余额是否足够
            BigDecimal usableTotalBalance =  usableBalanceDto.getUsableTotalBalance();
            if(usableTotalBalance.compareTo(amount) < 0){
                log.error("消费储值卡余额不足{}", dto.getMemberNo());
                throw new ServiceException(RespCodeEnum.CODE_1005);
            }
            //3.消费
            List<MemberStoreRechargeRuleRecordDto> usableRecords =  usableBalanceDto.getUsableRecords();
            return consumeHandler.consumeStore(usableRecords,dto);
        }finally {
            rLock.unlock();
        }
    }

    @Override
    public MemberStoreConsumeCalDto consumeStoreCal(MemberMemberStoreConsumeDto dto) {
        //查询储值的可用余额
        MemberStoreBalanceResult result =  memberStoreHandler.calculateBalance(storeConverter.convert(dto));
        if(!result.isSuccess()){
            return new MemberStoreConsumeCalDto(result.getResult().toString());
        }
        //1.校验可用余额是否足够
        MemberMasterUsableBalanceDto usableBalanceDto= result.getUsableBalanceDto();
        BigDecimal usableTotalBalance =  usableBalanceDto.getUsableTotalBalance();
        if(usableTotalBalance.compareTo(dto.getAmount())<0){
            return new MemberStoreConsumeCalDto(StoreUseResultEnum.Insufficient_Balance.toString());
        }

        MemberStoreCalContext calWrapper = consumeHandler.consumePreCal(usableBalanceDto.getUsableRecords(),dto);
        List<MemberStoreRechargeRecordBalanceDto> capitalBalances = calWrapper.getCapitalBalances();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = calWrapper.getGiftBalances();
        BigDecimal usedCapitalAmount = capitalBalances.stream().map(MemberStoreRechargeRecordBalanceDto::getConsumeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal usedGiftAmount = giftBalances.stream().map(MemberStoreRechargeRecordGiftBalanceDto::getConsumeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
       return MemberStoreConsumeCalDto.builder()
                        .result(StoreUseResultEnum.Success.toString())
                          .usedCapitalAmount(usedCapitalAmount)
                          .usedGiftAmount(usedGiftAmount)
                          .usableTotalBalance(usableBalanceDto.getUsableTotalBalance())
                          .usableTotalCapitalBalance(usableBalanceDto.getUsableTotalCapitalBalance())
                         .usableTotalGiftBalance(usableBalanceDto.getUsableTotalGiftBalance()).build();
    }

    @Override
    public MemberStoreRecordOPResultDto freeze(MemberMemberStoreFreezeDto dto) {
        //2.获取消费锁，锁的维度为会员编号
        String lockKey = SystemConstant.RECHARGE_CONSUME_LOCK+dto.getMemberNo();
        RLock rLock = redissonLockClient.lock(lockKey, 3L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getMemberNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "储值余额处理中，请稍后重试");
        }
        try {
            GetMemberUsableDto memberDto = storeConverter.convert(dto);
            memberDto.setIsNeedUsage(false);
            MemberStoreBalanceResult result = memberStoreHandler.calculateBalance(memberDto);
            if(!result.isSuccess()){
                throw new ServiceException(RespCodeEnum.Store_UnMatch_Error);
            }
            MemberMasterUsableBalanceDto balanceDto = result.getUsableBalanceDto();
            BigDecimal totalCapitalBalance =  balanceDto.getTotalCapitalBalance();
            BigDecimal totalGiftBalance =  balanceDto.getTotalGiftBalance();
            if(totalCapitalBalance.compareTo(dto.getCapitalAmount()) < 0 ){
                log.error("会员号：{} 冻结储值卡本金余额不足 totalCapitalBalance:{},capitalAmount:{}", dto.getMemberNo(),totalCapitalBalance,dto.getCapitalAmount());
                throw new ServiceException(RespCodeEnum.CODE_1005);
            }
            if(totalGiftBalance.compareTo(dto.getGiftAmount()) < 0){
                log.error("会员号：{}冻结储值卡礼金余额不足剩余余额：totalGiftBalance：{} giftAmount:{}", dto.getMemberNo(),totalGiftBalance,dto.getGiftAmount());
                throw new ServiceException(RespCodeEnum.CODE_1011);
            }
            //构建所有可用的储值规则
            //3.消费
            List<MemberStoreRechargeRuleRecordDto> usableRecords =  balanceDto.getUsableRecords();
            return consumeHandler.freeze(usableRecords,dto);
        } finally {
            rLock.unlock();
        }
    }

    @Override
    public MemberStoreRecordOPResultDto unFreeze(UnfreezeConsumeRecordNoDto dto) {
        //2.获取消费锁，锁的维度为会员编号
        String lockKey = SystemConstant.RECHARGE_CONSUME_LOCK+dto.getMemberNo();
        RLock rLock = redissonLockClient.lock(lockKey, 3L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getMemberNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "储值余额处理中，请稍后重试");
        }
        try {
            MemberStoreRechargeConsumeRecord consumeRecord = consumeRecordService.getByRecordNo(dto.getMemberNo(), dto.getConsumeRecordNo());
            if(consumeRecord ==null){
                throw new ServiceException(RespCodeEnum.CODE_1012);
            }
            MemberStoreRechargeFreezeDetail freezeDetail = freezeDetailBiz.getByRecord(dto.getConsumeRecordNo(), dto.getMemberNo());
            if(freezeDetail ==null){
                throw new ServiceException(RespCodeEnum.CODE_1012);
            }
            if(StringUtils.isNotEmpty(freezeDetail.getUnfreezeDate())){
                throw new ServiceException(RespCodeEnum.CODE_1013);
            }
            freezeDetail.setUnfreezeDate(LocalDate.now().toString());
            freezeDetail.setUnfreezeUser(dto.getOperator());
            freezeDetail.setUnfreezeReason(dto.getUnfreezeReason());
            MemberStoreConsumeWrapper wrapper = new MemberStoreConsumeWrapper();
            wrapper.setConsumeRecord(consumeRecord);
            wrapper.setFreezeDetail(freezeDetail);
            consumeRecordService.unFreezeRecord(wrapper);
            MemberStoreRecordOPResultDto resultDto = new MemberStoreRecordOPResultDto();
            resultDto.setMemberNo(dto.getMemberNo());
            resultDto.setRecodeNo(consumeRecord.getConsumeRecordNo());
            return resultDto;
        }finally {
            rLock.unlock();
        }

    }

    @Override
    public MemberStoreConsumeRollBackResultDto consumeRollback(MemberStoreConsumeRollBackDto dto) {
        //1.参数校验
        if(StringUtils.isEmpty(dto.getBizConsumeNo()) & StringUtils.isEmpty(dto.getConsumeNo())){
            throw new ServiceException(RespCodeEnum.CODE_400);
        }

        //2.获取消费锁，锁的维度为会员编号
        String lockKey = SystemConstant.RECHARGE_CONSUME_LOCK+dto.getMemberNo();
        RLock rLock = redissonLockClient.lock(lockKey, 3L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getMemberNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "储值余额处理中，请稍后重试");
        }
        try {
          return   refundHandler.consumeRollback(dto);
        }finally {
            rLock.unlock();
        }
    }



}
