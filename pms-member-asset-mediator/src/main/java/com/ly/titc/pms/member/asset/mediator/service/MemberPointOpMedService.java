package com.ly.titc.pms.member.asset.mediator.service;

import com.ly.titc.pms.member.asset.mediator.entity.point.*;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 积分操作
 * @since 2024-12-12 16:50
 */
public interface MemberPointOpMedService {

    /**
     *积分记录操作
     */
    MemberRecordOPResultDto receive(ReceiveMemberPointDto dto);

    /**
     *积分记录撤回
     */
    MemberRecordOPResultDto receiveRollback(ReceiveRollBackMemberPointDto dto);

    /**
     * 消费积分
     */
    MemberRecordOPResultDto consume(ConsumeMemberPointDto dto);

    /**
     * 消费积分撤回
     */
    MemberRecordOPResultDto consumeRollback(ConsumeRollBackMemberPointDto dto);

    /**
     * 根据业务单号或者积分记录号查询积分消费记录
     */
    List<MemberPointDto> listConsumeRecords(ListMemberPointConsumeForBusinessDto dto);
}
