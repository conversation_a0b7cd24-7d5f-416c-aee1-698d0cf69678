package com.ly.titc.pms.member.asset.mediator.entity.store;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:54
 */
@Data
@Accessors(chain = true)
public class MemberStoreRecordOPResultDto {
    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 充值记录号
     */
    private String recodeNo;

}
