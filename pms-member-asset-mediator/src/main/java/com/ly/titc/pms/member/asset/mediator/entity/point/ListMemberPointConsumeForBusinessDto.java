package com.ly.titc.pms.member.asset.mediator.entity.point;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetBusinessTypeEnum;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-29 13:24
 */
@Data
@Accessors(chain = true)
public class ListMemberPointConsumeForBusinessDto extends BaseMemberDto {

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    @LegalEnum(methodName = "getType",message = "业务类型不正确", target = AssetBusinessTypeEnum.class)
    private String businessType;

    /**
     * 业务订单编号 （业务单号和积分记录二选一）
     */
    private String businessNo;

    /**
     * 积分记录号 （业务单号和积分记录二选一）
     */
    private String receiverNo;
}
