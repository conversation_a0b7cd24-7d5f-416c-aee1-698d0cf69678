package com.ly.titc.pms.member.asset.mediator.handler;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.asset.com.constant.SystemConstant;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.utils.CommonUtil;
import com.ly.titc.pms.member.asset.dal.entity.po.*;
import com.ly.titc.pms.member.asset.entity.MemberPointConsumeRecordWrapper;
import com.ly.titc.pms.member.asset.mediator.convert.MemberPointMedConverter;
import com.ly.titc.pms.member.asset.mediator.entity.point.ConsumeMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberRecordOPResultDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberPointReceiveRecordBalanceDto;
import com.ly.titc.pms.member.asset.service.MemberPointRecordOpService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 16:21
 */
@Slf4j
@Component
public class MemberPointConsumeHandler {
    @Resource(type = MemberPointMedConverter.class)
    private MemberPointMedConverter  converter;

    @Resource(type = MemberPointRecordOpService.class)
    private MemberPointRecordOpService recordOpService;

    /**
     * 消费储值卡
     * @param usableRecords
     */
    @Transactional(rollbackFor = Exception.class)
    public MemberRecordOPResultDto consume(List<MemberPointReceiveRecord> usableRecords, ConsumeMemberPointDto dto){
        //根据创建时间升序排序，先消耗先充值的记录
        usableRecords.sort(Comparator.comparing(MemberPointReceiveRecord::getGmtCreate));
        List<MemberPointReceiveRecordBalanceDto> usedRecordBalances = new ArrayList<>();
        //消费积分
        Integer remainScore =  consumeRule(usableRecords,usedRecordBalances,dto.getScore());
        if(remainScore>0 || CollectionUtils.isEmpty(usedRecordBalances)){
            throw new ServiceException(RespCodeEnum.CODE_2001);
        }
        MemberPointConsumeRecordWrapper recordWrapper=  wrapper(usedRecordBalances,dto,usableRecords);
        recordOpService.saveConsume(recordWrapper);
        MemberRecordOPResultDto resultDto = new MemberRecordOPResultDto();
        resultDto.setRecordNo(recordWrapper.getConsumeRecord().getConsumeRecordNo());
        return resultDto;
    }

    /**
     * 保存消费记录
     **/
    private MemberPointConsumeRecordWrapper wrapper(List<MemberPointReceiveRecordBalanceDto> usedRecordBalances, ConsumeMemberPointDto dto, List<MemberPointReceiveRecord> usableRecords){
        MemberPointConsumeRecordWrapper wrapper = new MemberPointConsumeRecordWrapper();
        MemberPointConsumeRecord consumeRecord = converter.convertConsumeRecord(dto);
        consumeRecord.setConsumeRecordNo(CommonUtil.generateUniqueNo(SystemConstant.POINT_NO_PREFIX));
        Map<String,MemberPointReceiveRecord> recordMap = usableRecords.stream().collect(Collectors.toMap(MemberPointReceiveRecord::getReceiveRecordNo, Function.identity()));
        List<MemberPointReceiveConsumeMapping> mappings = new ArrayList<>();
        List<MemberPointReceiveRecord> receiveRecords = new ArrayList<>();
        List<MemberPointRecord> needUpdatePointRecords = new ArrayList<>();
        usedRecordBalances.forEach(balanceDto -> {
            MemberPointReceiveConsumeMapping consumeMapping = converter.convert(consumeRecord);
            consumeMapping.setScore(balanceDto.getConsumeScore());
            consumeMapping.setReceiveRecordNo(balanceDto.getReceiveRecordNo());
            mappings.add(consumeMapping);
            MemberPointReceiveRecord receiveRecord =  recordMap.get(balanceDto.getReceiveRecordNo());
            receiveRecord.setBalanceScore(balanceDto.getBalanceScore());
            receiveRecord.setModifyUser(dto.getOperator());
            receiveRecords.add(receiveRecord);
            MemberPointRecord pointRecord = new MemberPointRecord();
            pointRecord.setMemberNo(receiveRecord.getMemberNo());
            pointRecord.setRecordNo(balanceDto.getReceiveRecordNo());
            pointRecord.setBalanceScore(balanceDto.getBalanceScore());
            pointRecord.setModifyUser(dto.getOperator());
            //原则上版本号两个记录的版本号是一致的
            pointRecord.setVersion(receiveRecord.getVersion());
            needUpdatePointRecords.add(pointRecord);
        });

        MemberPointRecord pointRecord = converter.convertPoint( consumeRecord);
        pointRecord.setScore(-pointRecord.getScore());
        pointRecord.setActionType(consumeRecord.getConsumeType());
        pointRecord.setActionNo(consumeRecord.getConsumeRecordNo());
        wrapper.setConsumeRecord(consumeRecord);
        wrapper.setMappings(mappings);
        wrapper.setPointRecord(pointRecord);
        wrapper.setReceiveRecords(receiveRecords);
        wrapper.setNeedUpdatePointRecords(needUpdatePointRecords);
        return wrapper;
    }





    /**
     * 消费规则下的储值记录
     * @param recordDtoList
     * @param score 消费金额
     */
    private Integer consumeRule(List<MemberPointReceiveRecord> recordDtoList, List<MemberPointReceiveRecordBalanceDto> usedRecordBalances,Integer score){
        if(CollectionUtils.isEmpty(recordDtoList)){
            return score;
        }

        for(MemberPointReceiveRecord record : recordDtoList){
            if(record.getBalanceScore()<=0){
                break;
            }
            //判断是否过期
            if (StringUtils.isNotEmpty(record.getExpireDate() )&& LocalDateUtil.parseByNormalDate(record.getExpireDate()).isBefore(LocalDateUtil.now())) {
                log.info("该条充值记录礼金已过期：memberNo:{},rechargeRecordNo{}",record.getMemberNo(),record.getReceiveRecordNo());
                continue;
            }
            if(score<=0){
                break;
            }
            MemberPointReceiveRecordBalanceDto balanceDto = consumeScoreRecode(record,score);

            score = balanceDto.getRemainScore();
            usedRecordBalances.add(balanceDto);
        }
        return score;
    }

    /**
     * 积分扣减
     */
    private MemberPointReceiveRecordBalanceDto consumeScoreRecode(MemberPointReceiveRecord recordDto, Integer score){
        Integer balance = recordDto.getBalanceScore();
        RemainClass remainObj = calRemainBalance(balance,score);
        //设置当前剩余
        MemberPointReceiveRecordBalanceDto balanceDto = new MemberPointReceiveRecordBalanceDto();
        balanceDto.setReceiveRecordNo(recordDto.getReceiveRecordNo());
        balanceDto.setConsumeScore(remainObj.getConsumeScore());
        balanceDto.setRemainScore(remainObj.getRemainScore());
        balanceDto.setBalanceScore(remainObj.getRemainBalance());
        return balanceDto;
    }



    private RemainClass calRemainBalance(Integer balance,Integer score){
        RemainClass remainObj = new RemainClass();
        Integer remainBalance = 0;
        Integer remainScore =  0;
        Integer consumeScore = 0;
        if(remainBalance.compareTo(remainScore)>=0){
            remainBalance =  balance-score;
            consumeScore= score;
        }else{
            remainScore = score-remainBalance;
            consumeScore = remainBalance;
        }
        remainObj.setRemainBalance(remainBalance);
        remainObj.setRemainScore(remainScore);
        remainObj.setConsumeScore(consumeScore);
        return remainObj;
    }

    @Getter
    @Setter
    static class RemainClass{
        /**
         * 剩余余额
         */
        private Integer remainBalance;
        /**
         * 剩余待扣
         */
        private Integer remainScore;

        /**
         * 本次消费
         */
        private  Integer consumeScore;

    }


}
