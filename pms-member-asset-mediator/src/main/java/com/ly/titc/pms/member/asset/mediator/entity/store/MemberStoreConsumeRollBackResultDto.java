package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@Accessors(chain = true)
public class MemberStoreConsumeRollBackResultDto {
    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 退款记录号
     */
    private String refundRecordNo;




}
