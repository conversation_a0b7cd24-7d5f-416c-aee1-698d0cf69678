package com.ly.titc.pms.member.asset.mediator.entity.point;


import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 16:56
 */
@Data
@Accessors(chain = true)
public class ReceiveMemberPointDto extends BaseMemberDto {


    /**
     * 获得积分的类型 RECEIVE:获得 RECOVERY:回收
     */
    private String receiveType;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    private String platformChannel;
    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP ,营销活动 SPM
     */
    private String businessType;

    /**
     * 业务订单编号
     */
    private String businessNo;

    private String actionType;

    /**
     * 积分项目
     */
    private String actionItem;

    /**
     * 积分项目描述
     */
    private String actionItemDesc;

    /**
     * 积分数（加为正，减为负）
     */
    private Integer score;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 过期时间 yyyy-MM-dd
     */
    private String expireDate;

    /**
     * 备注 原因
     */
    private String remark;

    private String operator;

}
