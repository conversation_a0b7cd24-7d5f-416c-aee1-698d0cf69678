package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import com.ly.titc.pms.member.asset.dubbo.enums.StoreUseResultEnum;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 13:40
 */
@Data
@Accessors(chain = true)
public class MemberStoreAccountDto {


    /**
     * 计算结果枚举
     */
    private StoreUseResultEnum result;

    /**
     * 总可用，不包含指定平台和指定主体的账户金额信息
     */
    private MemberTotalAmountDto totalAmountDto;

    /**
     * 指定主体和平台账户金额信息
     */
    private MemberMasterAmountDto masterAmountDto;


}
