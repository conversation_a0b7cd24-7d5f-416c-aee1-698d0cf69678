package com.ly.titc.pms.member.asset.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.asset.mediator.entity.store.*;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:03
 */
public interface MemberStoreRechargeConsumeMedService {

    /**
     * 查询指定业务单号的使用记录
     */
    MemberStoreConsumeRecordDto listByBusinessNo(ListMemberStoreConsumeByBusinessNoDto dto);

    /**
     * 分页查询会员储值消费记录
     */
    Pageable<MemberStoreConsumeRecordDto> pageConsumeRecord(PageMemberStoreConsumeDto dto);


    /**
     * 查询会员订单的消费记录
     */
    List<MemberTradeConsumeRecordDto> listRechargeConsumeRecord(String memberNo, List<String> tradeNoList);
}
