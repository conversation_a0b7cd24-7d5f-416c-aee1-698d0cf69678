package com.ly.titc.pms.member.asset.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.QueryMemberScopeUsageForAssetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberStoreUsageDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 15:26
 */
@Slf4j
@Component
public class MemberStoreUsageDecorator {

    @DubboReference(group = "${ecrm-dsf-dubbo-group}")
    private MemberStoreUsageDubboService memberStoreUsageDubboService;


    public List<MemberStoreUsageRuleAssetResp> listRuleForAsset(Integer masterType, String masterCode,String platformChannel){
        QueryMemberScopeUsageForAssetReq req = new QueryMemberScopeUsageForAssetReq();
        req.setScopeSource(ScopeSourceEnum.getSourceByMaster(masterType));
        req.setScopeSourceCode(masterCode);
        req.setTrackingId(UUID.randomUUID().toString());
        req.setPlatformChannel(platformChannel);
        Response<List<MemberStoreUsageRuleAssetResp>> response = memberStoreUsageDubboService.listRuleForAsset(req);
        return Response.getValidateData(response);
    }
}
