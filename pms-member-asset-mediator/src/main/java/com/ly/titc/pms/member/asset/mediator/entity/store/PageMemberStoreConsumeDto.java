package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BasePageMemberDto;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:06
 */
@Data
public class PageMemberStoreConsumeDto extends BasePageMemberDto {

    /**
     * 消费类型：pay:支付，feeze冻结, refund 退款
     */
    @NotNull(message = "消费类型不能为空")
    private String consumeType;


    /**
     * 消费开始时间
     */
    private String beginTime;

    /**
     * 消费结束时间
     */
    private String endTime;


}
