package com.ly.titc.pms.member.asset.mediator.rpc.dsf.mdm;

import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.HotelService;
import com.ly.titc.mdm.entity.request.hotel.GetHotelByVidReq;
import com.ly.titc.mdm.entity.request.hotel.ListHotelBaseInfosReq;
import com.ly.titc.mdm.entity.request.hotel.SelectHotelReq;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.HotelInfoResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName: HotelDecorator
 * @Description:
 * @since 2022/11/28 13:47
 */
@Slf4j
@Component
public class HotelDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private HotelService hotelService;

    private static final String S_NAME = "hotel";

    /**
     * 获取门店信息
     *
     * @param req
     * @return
     */
    public HotelInfoResp getHotelByVid(GetHotelByVidReq req) {
        return Response.getValidateData(hotelService.getHotelByVid(req));
    }

    /**
     * 获取酒店下拉列表
     *
     * @param req
     * @return
     */
    public List<SelectHotelResp> selectHotels(SelectHotelReq req) {
        return Response.getValidateData(hotelService.selectHotels(req));
    }

    public List<HotelBaseInfoResp> listHotelBaseInfos(List<String> hotelCodes) {
        List<Long> hotelVids = null;
        if (!CollectionUtils.isEmpty(hotelCodes)) {
            hotelVids = hotelCodes.stream().map(Long::valueOf).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
        String trackingId = UUID.randomUUID().toString();
        ListHotelBaseInfosReq req = new ListHotelBaseInfosReq();
        req.setHotelVids(hotelVids)
                .setTrackingId(trackingId);

        log.info("mdm查询酒店基础信息请求参数，trackingId：{}", trackingId);
        Response<List<HotelBaseInfoResp>> listResponse = hotelService.listHotelBaseInfos(req);
        if (null == listResponse || !"200".equals(listResponse.getCode())) {
            log.info("mdm查询酒店基础信息请求返回错误，返回值：{}，trackingId：{}", listResponse, trackingId);
            return null;
        }
        return listResponse.getData();
    }
}
