package com.ly.titc.pms.member.asset.mediator.handler;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-29 13:28
 */
@Slf4j
@Component
public class AssetCheckHandler {
    /**
     * 校验参数
     */
    public void checkArgs(BaseMemberDto dto){
        if(dto.getMasterType().equals(MasterTypeEnum.HOTEL.getType())){
            if(StringUtils.isEmpty(dto.getHotelCode())){
                throw new ServiceException("酒店编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
            if(StringUtils.isEmpty(dto.getBlocCode())){
                throw new ServiceException("集团编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
            if(!dto.getMasterCode().equals(dto.getHotelCode())){
                throw new ServiceException("酒店编码与主体编码不一致", RespCodeEnum.CODE_400.getCode());
            }
        }

        if(dto.getMasterType().equals(MasterTypeEnum.BLOC.getType())){
            if(StringUtils.isEmpty(dto.getBlocCode())){
                throw new ServiceException("集团编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
            if(!dto.getMasterCode().equals(dto.getBlocCode())){
                throw new ServiceException("集团编码与主体编码不一致", RespCodeEnum.CODE_400.getCode());
            }
        }
        if(dto.getMasterType().equals(MasterTypeEnum.PUB.getType())){
            if(StringUtils.isEmpty(dto.getClubCode())){
                throw new ServiceException("酒馆组编码不能为空", RespCodeEnum.CODE_400.getCode());
            }
            if(!dto.getMasterCode().equals(dto.getClubCode())){
                throw new ServiceException("酒馆组编码与主体编码不一致", RespCodeEnum.CODE_400.getCode());
            }
        }

    }


    /**
     * 校验业务和记录号参数
     */
    public void checkBusinessNoAndRecordNo(String businessType, String businessNo, String recordNo) {
        if (StringUtils.isEmpty(businessNo) && StringUtils.isEmpty(recordNo)) {
            throw new ServiceException("业务单号和积分记录号不能同时为空", RespCodeEnum.CODE_400.getCode());
        }

        if(StringUtils.isNotEmpty(businessNo) && StringUtils.isEmpty(businessType)){
            throw new ServiceException("业务单号不为空时，业务类型不能为空", RespCodeEnum.CODE_400.getCode());
        }
    }
}
