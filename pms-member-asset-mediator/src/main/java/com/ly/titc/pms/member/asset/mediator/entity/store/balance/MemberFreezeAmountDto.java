package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值使用余额信息
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class MemberFreezeAmountDto {

    /**
     * 充值使用类型
     */
    private Integer masterType;

    /**
     * 充值使用类型编码
     */
    private String masterCode;


    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 平台
     */
    private String platform;

    /**
     * （归属+平台渠道）总冻结储值（本金余额+赠送余额）
     */
    private BigDecimal totalFreezeAmount = BigDecimal.ZERO;

    /**
     * （归属+平台渠道）总冻结本金
     */
    private BigDecimal totalFreezeCapitalAmount = BigDecimal.ZERO;

    /**
     * （归属+平台渠道）总冻结礼金
     */
    private BigDecimal totalFreezeGiftAmount = BigDecimal.ZERO;


}
