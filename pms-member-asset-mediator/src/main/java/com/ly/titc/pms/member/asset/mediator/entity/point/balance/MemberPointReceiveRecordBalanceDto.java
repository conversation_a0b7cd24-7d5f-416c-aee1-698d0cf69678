package com.ly.titc.pms.member.asset.mediator.entity.point.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 20:25
 */
@Data
@Accessors
public class MemberPointReceiveRecordBalanceDto {

    /**
     * 获得积分记录号
     */
    private String receiveRecordNo;

    /**
     * 剩余积分
     */
    private Integer balanceScore;

    /**
     * 本次消费积分
     */
    private Integer consumeScore;

    /**
     * 剩余待扣金粉
     */
    private Integer remainScore;


}
