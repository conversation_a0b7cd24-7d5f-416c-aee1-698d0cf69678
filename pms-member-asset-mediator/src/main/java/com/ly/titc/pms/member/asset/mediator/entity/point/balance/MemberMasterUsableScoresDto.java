package com.ly.titc.pms.member.asset.mediator.entity.point.balance;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreRechargeRuleRecordDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 可用积分
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class MemberMasterUsableScoresDto {

    /**
     * 使用类型
     */
    private Integer masterType;

    /**
     * 使用类型编码
     */
    private String masterCode;

    /**
     * 平台渠道
     */
    private String platformChannel;
    /**
     * 总剩余积分
     */
    private Integer totalScoreBalance=0;

    /**
     * 总可用积分
     */
    private Integer usableTotalScore =0;

    /**
     * 可用的积分记录和对应规则
     */
    private List<MemberPointReceiveRecord> usableRecords;


    /**
     * 当前这条记录的储值使用规则
     */
    private MemberPointUsageRuleDto usageRuleDto;


}
