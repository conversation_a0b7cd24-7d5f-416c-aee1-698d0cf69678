package com.ly.titc.pms.member.asset.mediator.entity.store;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author：rui
 * @name：MemberTradeConsumeRecordDto
 * @Date：2024-12-6 11:36
 * @Filename：MemberTradeConsumeRecordDto
 */
@Data
public class MemberTradeConsumeRecordDto {

    /**
     * 订单号
     */
    private String tradeNo;

    /**
     * 已消费储值金额
     */
    private BigDecimal consumeAmount;

    /**
     * 已消费礼金金额
     */
    private BigDecimal consumeGiftAmount;

    /**
     * 储值本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 储值礼金
     */
    private BigDecimal giftAmount;

    /**
     * 赠送礼金过期时间
     */
    private String giftExpireDate;
}
