package com.ly.titc.pms.member.asset.mediator.handler;

import com.alibaba.fastjson.JSON;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 15:49
 */
@Slf4j
@Component
public class MemberStoreExpireHandler {

    /**
     * 储值过期
     */

    public void expireMemberStore(List<MemberStoreRechargeRecord> records) {
        //todo 过期的记录发送MQ计算余额并保存过期记录

        log.info("expire member store:{}", JSON.toJSONString(records));
    }

    /**
     * 积分过期
     */
    public void expireMemberPoint(List<MemberPointReceiveRecord> records){
        //todo 过期的记录发送MQ 并计算积分余额
    }
}
