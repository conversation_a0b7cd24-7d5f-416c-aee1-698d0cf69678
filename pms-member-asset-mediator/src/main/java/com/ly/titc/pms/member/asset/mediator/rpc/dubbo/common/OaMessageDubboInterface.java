package com.ly.titc.pms.member.asset.mediator.rpc.dubbo.common;

import com.ly.titc.cc.dubbo.entity.request.ioa.SendTextReq;
import com.ly.titc.cc.dubbo.interfaces.OaMessageDubboService;
import com.ly.titc.common.entity.Response;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @classname OaMessageDubboInterface
 * @descrition
 * @since 2021/10/8 下午8:13
 */
@Slf4j
@Component
public class OaMessageDubboInterface {

	@DubboReference
	public OaMessageDubboService oaMessageDubboService;

	/**
	 * 发送信息
	 * @param trackingId  追踪id
	 * @param sendType    发送渠道类型：企业微信（qywx）、iOA(iOA)，多个以‘,’分隔
	 * @param title       标题，最长不超过50字（iOA必传，企业微信不必传）
	 * @param description 描述，最长不超过100字（iOA必传，企业微信不必传）
	 * @param content     内容，（企业微信最长不超过2048个字节，iOA最长不超过250个字）
	 * @param jobNumbers  接收人工号，最多支持100个
	 * @return
	 */
	public String sendText(String trackingId, String sendType, String title, String description, String content, List<String> jobNumbers) {

		SendTextReq req = new SendTextReq();
		req.setSendType(sendType)
				.setTitle(title)
				.setContent(content)
				.setJobNumbers(jobNumbers)
				.setTrackingId(trackingId);
		if (!StringUtils.isEmpty(description)) {
			req.setDescription(description);
		}
		Response<String> response = oaMessageDubboService.sendText(req);
		return Response.getValidateData(response);
	}
}
