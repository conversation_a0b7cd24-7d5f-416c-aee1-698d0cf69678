package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:06
 */
@Data
@Accessors(chain = true)
public class MemberMemberStoreFreezeDto extends GetMemberUsableDto {

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    private BigDecimal giftAmount;
    /**
     * 冻结有效期 yyyy-mm-dd
     */
    private String freezeDate;
    /**
     * 是否长期冻结 0否 1是
     */
    @NotNull(message = "是否长期冻结不能为空")
    private Integer isFreezeLong;

    /**
     * 消费类型：pay:支付，
     * @see com.ly.titc.pms.member.asset.dubbo.enums.AssetConsumeTypeEnum
     */
    private String consumeType;

    /**
     * 消费描述
     */
    private String consumeDesc;

    /**
     * 业务消费号（订单号）(来源系统+业务消费号幂等)
     */
    private String bizConsumeNo;


    /**
     * 来源系统：会员-MEMBER，商品部-SHOP 餐饮-FOOD 微订房-WEBOOKNG 收银台——CASHIER
     */
    private String sourceSystem;

    /**
     * 业务请求体
     */
    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

}
