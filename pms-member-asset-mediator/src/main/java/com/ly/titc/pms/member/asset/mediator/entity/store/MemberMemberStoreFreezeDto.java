package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:06
 */
@Data
@Accessors(chain = true)
public class MemberMemberStoreFreezeDto extends GetMemberUsableDto {

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    private BigDecimal giftAmount;
    /**
     * 冻结有效期 yyyy-mm-dd
     */
    private String freezeDate;
    /**
     * 是否长期冻结 0否 1是
     */
    @NotNull(message = "是否长期冻结不能为空")
    private Integer isFreezeLong;

    /**
     * 消费类型：pay:支付，
     * @see com.ly.titc.pms.member.asset.dubbo.enums.AssetConsumeTypeEnum
     */
    private String consumeType;

    /**
     * 消费描述
     */
    private String consumeDesc;

    /**
     * 业务线唯一号
     */
    private String businessNo;

    /**
     *  WXBOOKINGPAY(微订房支付) ,PMSPAY(PMS支付)
     */
    private String businessType;

    /**
     * 业务请求体
     */
    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

}
