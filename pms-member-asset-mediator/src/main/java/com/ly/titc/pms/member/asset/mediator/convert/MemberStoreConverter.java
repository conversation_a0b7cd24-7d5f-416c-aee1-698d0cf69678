package com.ly.titc.pms.member.asset.mediator.convert;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.member.asset.com.constant.SystemConstant;
import com.ly.titc.pms.member.asset.com.utils.CommonUtil;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import com.ly.titc.pms.member.asset.dal.entity.dto.RechargeWrapper;
import com.ly.titc.pms.member.asset.dal.entity.po.*;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.*;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.PageMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.*;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:08
 */
@Mapper(componentModel = "spring")
public interface MemberStoreConverter {

    BaseMemberDto convert(BaseMemberReq req);
    MemberStoreRechargeDto convert(MemberStoreRechargeReq req);

    MemberStoreRechargeRecordResp convert(MemberStoreRecordOPResultDto dto);

    GetMemberTotalBalanceMemberDto convert(GetMemberTotalBalanceMemberReq req);

    MemberTotalAmountResp convert(MemberTotalAmountDto dto);

    GetMemberUsableDto convert(GetMemberUsableReq req);


    default MemberStoreRechargeRecord convert(MemberStoreRechargeDto dto, String rechargeRecordNo) {
        MemberStoreRechargeRecord record = convertBase(dto);
        //充值余额为当前的充值的金额
        record.setCapitalBalance(dto.getCapitalAmount());
        record.setGiftBalance(dto.getGiftAmount());
        record.setTotalBalance(dto.getCapitalAmount().add(dto.getGiftAmount()));
        record.setTotalAmount(dto.getCapitalAmount().add(dto.getGiftAmount()));
        record.setRechargeRecordNo(rechargeRecordNo);
        return record;
    }

    default RechargeWrapper convert(MemberStoreRechargeRecord originalRecord,MemberStoreRechargeRollBackDto dto){
        RechargeWrapper wrapper = new RechargeWrapper();
        BigDecimal balance = new BigDecimal(0);
        originalRecord.setCapitalBalance(balance);
        originalRecord.setGiftBalance(balance);
        originalRecord.setTotalBalance(balance);
        originalRecord.setModifyUser(dto.getOperator());
        MemberStoreRechargeRecord record = convertPO(originalRecord);
        record.setTradeType(dto.getTradeType());
        record.setRechargeRecordNo(CommonUtil.generateUniqueNo(SystemConstant.RECHARGE_NO_PREFIX));
        record.setCapitalBalance(balance);
        record.setGiftBalance(balance);
        record.setTotalBalance(balance);
        record.setModifyUser(dto.getOperator());
        record.setCreateUser(dto.getOperator());
        wrapper.setAddRecord(record);
        wrapper.setUpdateRecord(originalRecord);
        wrapper.setCapitalAmount(originalRecord.getCapitalAmount().negate());
        wrapper.setGiftAmount(originalRecord.getGiftAmount().negate());
        wrapper.setMemberNo(originalRecord.getMemberNo());
        wrapper.setVersion(originalRecord.getVersion());
        return wrapper;
    }

    MemberStoreRechargeRecord convertPO(MemberStoreRechargeRecord originalRecord);

    MemberStoreRechargeRollBackDto convert(MemberStoreRechargeRollBackReq req);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    MemberStoreRechargeRecord convertBase(MemberStoreRechargeDto dto);

    MemberStoreRecordOPResultDto convert(MemberStoreRechargeRecord po);

    List<MemberStoreUsageRuleDto> convert(List<MemberStoreUsageRuleAssetResp> ruleAssetResps);

    GetMemberUsableDto convertTotal(GetMemberTotalBalanceMemberDto dto);

    @Mappings({
            @Mapping(target = "totalAmountResp", source = "totalAmountDto"),
            @Mapping(target = "masterAmountResp", source = "masterAmountDto")
    })
    MemberStoreAccountResp convert(MemberStoreAccountDto dto);

    GetMemberUsableDto convert(MemberMemberStoreConsumeDto dto);

    MemberStoreRechargeRuleRecordDto convertRecord(MemberStoreRechargeRecord matchRecord);

    List<MemberStoreRechargeRuleRecordDto> convertRe(List<MemberStoreRechargeRecord> records);

    MemberStoreUsageRuleRechargeRecordDto convertRuleRecord(MemberStoreUsageRuleDto singleRule);

    List<MemberStoreRechargeRecord> convertDtoList(List<MemberStoreRechargeRuleRecordDto> recordDtoList);

    @Mappings({
            @Mapping(target = "consumeDesc", source = "goodsDes")
    })
    MemberMemberStoreConsumeDto convert(MemberStoreConsumeReq req);

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator"),

    })
    MemberStoreRechargeConsumeRecord convertConsumeRecord(MemberMemberStoreConsumeDto dto);
    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator"),

    })
    MemberStoreRechargeConsumeRecord convertConsumeRecord(MemberMemberStoreFreezeDto dto);


    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator"),

    })
    MemberStoreRechargeConsumeMapping convertMapping(MemberMemberStoreConsumeDto dto);

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator"),

    })
    MemberStoreRechargeConsumeMapping convertMapping(MemberMemberStoreFreezeDto dto);


    MemberStoreConsumeRecordDto convertConsumeRecordDtoItem(MemberStoreRechargeConsumeRecord po, String masterName);

    default List<MemberStoreConsumeRecordDto> convertConsumeRecordDto(List<MemberStoreRechargeConsumeRecord> dtoList,
                                                                      List<MemberStoreRechargeFreezeDetail> freezeDetailList,
                                                                      Map<String, String> hotelNameMap) {
        Map<String, MemberStoreRechargeFreezeDetail> map = freezeDetailList.stream().collect(Collectors.toMap(MemberStoreRechargeFreezeDetail::getConsumeRecordNo, Function.identity()));
        return dtoList.stream().map(dto -> {
            MemberStoreConsumeRecordDto dtoItem = convertConsumeRecordDtoItem(dto, hotelNameMap.getOrDefault(dto.getMasterCode(), "集团"));
            dtoItem.setUnfreezeDate(map.get(dto.getConsumeRecordNo()).getUnfreezeDate());
            dtoItem.setUnfreezeUser(map.get(dto.getConsumeRecordNo()).getUnfreezeUser());
            dtoItem.setUnfreezeReason(map.get(dto.getConsumeRecordNo()).getUnfreezeReason());
            dtoItem.setIsFreezeLong(map.get(dto.getConsumeRecordNo()).getIsFreezeLong());
            dtoItem.setFreezeDate(map.get(dto.getConsumeRecordNo()).getFreezeDate());
            if (StringUtils.isEmpty(map.get(dto.getConsumeRecordNo()).getUnfreezeUser())) {
                dtoItem.setFreezeState(1);
            } else {
                dtoItem.setFreezeState(0);
            }
            return dtoItem;
        }).collect(Collectors.toList());
    }

    PageMemberStoreConsumeDto convert(PageMemberStoreConsumeMemberReq req);

    List<MemberStoreConsumeRecordResp> convertMemberStoreConsumeRecordRespList(List<MemberStoreConsumeRecordDto> dtoList);

    MemberStoreConsumeRecordResp convertMemberStoreConsumeRecordResp(MemberStoreConsumeRecordDto dto);


    List<MemberTradeConsumeRecordResp> convertMemberTradeConsumeRecordRespList(List<MemberTradeConsumeRecordDto> dtoList);

    MemberTradeConsumeRecordResp convertMemberTradeConsumeRecordResp(MemberTradeConsumeRecordDto dto);

    @Mappings({
            @Mapping(target = "totalFreezeAmount",source = "totalFrozenAmount"),
            @Mapping(target = "totalFreezeCapitalAmount",source = "totalFrozenCapitalAmount"),
            @Mapping(target = "totalFreezeGiftAmount",source = "totalFrozenGiftAmount")
    })
    MemberTotalAmountDto convert(MemberStoreAccountInfo accountInfo);

    BaseMemberDto convert(GetMemberUsableDto dto);

    @Mappings({
            @Mapping(target = "masterType",source = "balanceDto.masterType"),
            @Mapping(target = "masterCode",source = "balanceDto.masterCode")
    })
    MemberMasterAmountDto convert(MemberMasterUsableBalanceDto balanceDto, MemberUsedAmountDto usedAmountDto);

    MemberStoreConsumeCalResp convert(MemberStoreConsumeCalDto consumeCalDto);

    @Mappings({
            @Mapping(target = "consumeDesc", source = "goodsDes")
    })
    MemberMemberStoreFreezeDto convert(MemberStoreFreezeReq req);

    GetMemberUsableDto convert(MemberMemberStoreFreezeDto dto);

    MemberStoreRechargeFreezeDetail convertFreeze(MemberMemberStoreFreezeDto dto);

    UnfreezeConsumeRecordNoDto convert(UnfreezeConsumeRecordNoReq req);

    PageMemberPointDto convertPageDto(PageMemberPointsFlowMemberReq request);

//    default List<MemberStoreRechargeRuleRecordDto> convertFreeze(List<MemberStoreRechargeRuleRecordDto> recordDtos,MemberMemberStoreFreezeDto dto){
//        //模拟使用规则
//        MemberStoreUsageRuleDto usageRuleDto = new  MemberStoreUsageRuleDto();
//        usageRuleDto.setRuleMode(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode());
//        usageRuleDto.setDeductionType(MemberStoreDeductionTypeEnum.RATE.getValue());
//        usageRuleDto.setDeductionRate(String.valueOf(Constant.HUNDRED));
//        usageRuleDto.setUsageMode(MemberStoreUsageModeEnum.ALL.getValue());
//        usageRuleDto.setUsageRuleId(String.valueOf(Constant.HUNDRED_THOUSAND));
//        usageRuleDto.setScopePlatformChannels(Collections.singletonList(dto.getPlatformChannel()));
//
//        recordDtos.forEach(dto1 -> {
//            dto1.setUsageRuleDto(usageRuleDto);
//            dto1.setUsageRuleId(usageRuleDto.getUsageRuleId());
//
//        });
//        return recordDtos;
//    }
}
