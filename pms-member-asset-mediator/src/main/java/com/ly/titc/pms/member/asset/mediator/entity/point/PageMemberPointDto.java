package com.ly.titc.pms.member.asset.mediator.entity.point;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @date 2025/6/5 19:40
 */
@Data
public class PageMemberPointDto {

    private String memberNo;

    private String masterCode;

    private Integer masterType;

    private String beginTime;

    private String endTime;

    /**
     * 页码
     */
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    private Integer pageSize = 20;


}
