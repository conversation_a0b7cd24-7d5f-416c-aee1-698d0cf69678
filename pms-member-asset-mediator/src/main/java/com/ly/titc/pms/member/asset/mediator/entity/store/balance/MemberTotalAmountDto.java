package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值总余额信息
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class MemberTotalAmountDto {

    private String memberNo;

    /**
     *  会员总储值（本金余额+赠送余额）
     */
    private BigDecimal totalAmount =BigDecimal.ZERO;

    /**
     * 累计充值储值本金
     */
    private BigDecimal totalCapitalAmount =BigDecimal.ZERO;

    /**
     * 累计充值礼金
     */
    private BigDecimal totalGiftAmount =BigDecimal.ZERO;


    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance=BigDecimal.ZERO;

    /**
     * 总本金余额
     */
    private BigDecimal totalCapitalBalance =BigDecimal.ZERO;

    /**
     * 总赠送余额
     */
    private BigDecimal totalGiftBalance =BigDecimal.ZERO;


    /**
     * 总过期礼金金额
     */
    private BigDecimal totalExpireGiftAmount =BigDecimal.ZERO;

    /**
     *  总使用储值（本金余额+赠送余额）
     */
    private BigDecimal totalUsedAmount =BigDecimal.ZERO;

    /**
     * 总使用本金
     */
    private BigDecimal totalUsedCapitalAmount =BigDecimal.ZERO;

    /**
     * 总使用礼金金额
     */
    private BigDecimal totalUsedGiftAmount=BigDecimal.ZERO;

    /**
     *  总冻结储值（本金余额+赠送余额）
     */
    private BigDecimal totalFreezeAmount =BigDecimal.ZERO;

    /**
     * 总冻结本金
     */
    private BigDecimal totalFreezeCapitalAmount =BigDecimal.ZERO;

    /**
     * 总冻结礼金金额
     */
    private BigDecimal totalFreezeGiftAmount =BigDecimal.ZERO;


}
