package com.ly.titc.pms.member.asset.mediator.service.impl;

import com.ly.titc.pms.member.asset.biz.MemberStoreAccountBiz;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreAccountInfo;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStorePeriodReq;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberMasterAmountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberMasterUsableBalanceDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberTotalAmountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreHandler;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoredMedService;
import com.ly.titc.pms.member.asset.service.MemberStoreAccountService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员储值Med服务实现
 *
 * <AUTHOR>
 * @date 2024/11/7 16:20
 */
@Slf4j
@Service
public class MemberStoredMedServiceImpl implements MemberStoredMedService {

    @Resource
    private MemberStoreAccountService storeAccountService;
    @Resource
    private MemberStoreConverter storeConverter;
    @Resource
    private MemberStoreHandler storeHandler;
    @Resource
    private MemberStoreAccountBiz memberStoreAccountBiz;


    @Override
    public MemberTotalAmountDto getTotalAccountAmount(BaseMemberDto dto) {
        MemberStoreAccountInfo accountInfo = storeAccountService.getByMemberNo(dto.getMemberNo());
        MemberTotalAmountDto amountDto = storeConverter.convert(accountInfo);
        if (amountDto == null) {
            return new MemberTotalAmountDto();
        } else {
            return amountDto;
        }
    }

    @Override
    public List<MemberTotalAmountDto> getTotalAccountAmountPeriod(MemberStorePeriodReq dto) {
        List<MemberStoreAccountInfo> memberStoreAccountInfos = memberStoreAccountBiz.getByMemberNos(dto.getMemberNoList(), dto.getSdate(), dto.getEdate());
        // 如果没有查询到数据，返回空列表
        if (CollectionUtils.isEmpty(memberStoreAccountInfos)) {
            return new ArrayList<>();
        }
        // 分组计算每个会员的储值信息
        Map<String, List<MemberStoreAccountInfo>> memberGroupMap = memberStoreAccountInfos
                .stream()
                .collect(Collectors.groupingBy(MemberStoreAccountInfo::getMemberNo));
        // 构建响应结果
        return memberGroupMap.entrySet()
                .stream()
                .map(entry -> {
                    String memberNo = entry.getKey();
                    List<MemberStoreAccountInfo> accountInfos = entry.getValue();
                    // 计算该会员在指定时间段内的各项储值金额
                    BigDecimal totalAmount = accountInfos.stream()
                            .map(info -> info.getTotalAmount() != null ? info.getTotalAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalCapitalAmount = accountInfos.stream()
                            .map(info -> info.getTotalCapitalAmount() != null ? info.getTotalCapitalAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalGiftAmount = accountInfos.stream()
                            .map(info -> info.getTotalGiftAmount() != null ? info.getTotalGiftAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalBalance = accountInfos.stream()
                            .map(info -> info.getTotalBalance() != null ? info.getTotalBalance() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalCapitalBalance = accountInfos.stream()
                            .map(info -> info.getTotalCapitalBalance() != null ? info.getTotalCapitalBalance() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalGiftBalance = accountInfos.stream()
                            .map(info -> info.getTotalGiftBalance() != null ? info.getTotalGiftBalance() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalExpireGiftAmount = accountInfos.stream()
                            .map(info -> info.getTotalExpireGiftAmount() != null ? info.getTotalExpireGiftAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalUsedAmount = accountInfos.stream()
                            .map(info -> info.getTotalUsedAmount() != null ? info.getTotalUsedAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalUsedCapitalAmount = accountInfos.stream()
                            .map(info -> info.getTotalUsedCapitalAmount() != null ? info.getTotalUsedCapitalAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalUsedGiftAmount = accountInfos.stream()
                            .map(info -> info.getTotalUsedGiftAmount() != null ? info.getTotalUsedGiftAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalFrozenAmount = accountInfos.stream()
                            .map(info -> info.getTotalFrozenAmount() != null ? info.getTotalFrozenAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalFrozenCapitalAmount = accountInfos.stream()
                            .map(info -> info.getTotalFrozenCapitalAmount() != null ? info.getTotalFrozenCapitalAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalFrozenGiftAmount = accountInfos.stream()
                            .map(info -> info.getTotalFrozenGiftAmount() != null ? info.getTotalFrozenGiftAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 构建返回对象
                    MemberTotalAmountDto resp = new MemberTotalAmountDto();
                    resp.setMemberNo(memberNo);
                    resp.setTotalAmount(totalAmount);
                    resp.setTotalCapitalAmount(totalCapitalAmount);
                    resp.setTotalGiftAmount(totalGiftAmount);
                    resp.setTotalBalance(totalBalance);
                    resp.setTotalCapitalBalance(totalCapitalBalance);
                    resp.setTotalGiftBalance(totalGiftBalance);
                    resp.setTotalExpireGiftAmount(totalExpireGiftAmount);
                    resp.setTotalUsedAmount(totalUsedAmount);
                    resp.setTotalUsedCapitalAmount(totalUsedCapitalAmount);
                    resp.setTotalUsedGiftAmount(totalUsedGiftAmount);
                    resp.setTotalFreezeAmount(totalFrozenAmount);
                    resp.setTotalFreezeCapitalAmount(totalFrozenCapitalAmount);
                    resp.setTotalFreezeGiftAmount(totalFrozenGiftAmount);

                    return resp;
                })
                .collect(Collectors.toList());
    }

    @Override
    public MemberStoreAccountDto getUsableMasterAccount(GetMemberUsableDto dto) {
        BaseMemberDto baseMemberDto = storeConverter.convert(dto);
        //获取总账户金额
        MemberTotalAmountDto totalAmountDto = this.getTotalAccountAmount(baseMemberDto);
        //获取归属主体和平台的可用余额
        MemberStoreBalanceResult storeBalanceResult =  storeHandler.calculateBalance(dto);
        //获取归属主体的总消费金额
        MemberUsedAmountDto usedAmountDto = storeHandler.calculateUsedAmount(dto);
        MemberMasterAmountDto masterAmountDto = storeConverter.convert(storeBalanceResult.getUsableBalanceDto(),usedAmountDto);
        MemberStoreAccountDto accountDto = new MemberStoreAccountDto();
        accountDto.setTotalAmountDto(totalAmountDto);
        accountDto.setMasterAmountDto(masterAmountDto);
        return accountDto;
    }
}
