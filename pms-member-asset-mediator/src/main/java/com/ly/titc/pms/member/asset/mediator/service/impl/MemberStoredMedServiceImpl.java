package com.ly.titc.pms.member.asset.mediator.service.impl;

import com.ly.titc.pms.member.asset.biz.MemberStoreAccountBiz;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreAccountInfo;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStorePeriodReq;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberMasterAmountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberMasterUsableBalanceDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberTotalAmountDto;
import com.ly.titc.pms.member.asset.mediator.handler.MemberStoreHandler;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoredMedService;
import com.ly.titc.pms.member.asset.service.MemberStoreAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员储值Med服务实现
 *
 * <AUTHOR>
 * @date 2024/11/7 16:20
 */
@Slf4j
@Service
public class MemberStoredMedServiceImpl implements MemberStoredMedService {

    @Resource
    private MemberStoreAccountService storeAccountService;
    @Resource
    private MemberStoreConverter storeConverter;
    @Resource
    private MemberStoreHandler storeHandler;
    @Resource
    private MemberStoreAccountBiz memberStoreAccountBiz;


    @Override
    public MemberTotalAmountDto getTotalAccountAmount(BaseMemberDto dto) {
        MemberStoreAccountInfo accountInfo = storeAccountService.getByMemberNo(dto.getMemberNo());
        MemberTotalAmountDto amountDto = storeConverter.convert(accountInfo);
        if (amountDto == null) {
            return new MemberTotalAmountDto();
        } else {
            return amountDto;
        }
    }

    @Override
    public List<MemberTotalAmountDto> getTotalAccountAmountPeriod(MemberStorePeriodReq dto) {
        List<MemberStoreAccountInfo> memberStoreAccountInfos = memberStoreAccountBiz.getByMemberNos(dto.getMemberNoList(), dto.getSdate(), dto.getEdate());
        // 分组计算每个会员的积分
        Map<String, List<MemberStoreAccountInfo>> memberGroupMap = memberStoreAccountInfos
                .stream()
                .collect(Collectors.groupingBy(MemberStoreAccountInfo::getMemberNo));
        // 构建响应结果
        return memberGroupMap.entrySet()
                .stream()
                .map(entry -> {
                    String memberNo = entry.getKey();
                    List<MemberStoreAccountInfo> accountInfos = entry.getValue();
                    // 计算该会员在指定时间段内的总积分
                    BigDecimal totalScore = accountInfos.stream()
                            .map(info -> info.getTotalCapitalAmount() != null ? info.getTotalCapitalAmount() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 

                    MemberTotalAmountDto resp = new MemberTotalAmountDto();
                    resp.setMemberNo(memberNo);
                    resp.setTotalCapitalAmount(totalScore);
                    return resp;
                })
                .collect(Collectors.toList());
    }

    @Override
    public MemberStoreAccountDto getUsableMasterAccount(GetMemberUsableDto dto) {
        BaseMemberDto baseMemberDto = storeConverter.convert(dto);
        //获取总账户金额
        MemberTotalAmountDto totalAmountDto = this.getTotalAccountAmount(baseMemberDto);
        //获取归属主体和平台的可用余额
        MemberMasterUsableBalanceDto masterUsableBalanceDto = storeHandler.calculateBalance(dto);
        //获取归属主体的总消费金额
        MemberUsedAmountDto usedAmountDto = storeHandler.calculateUsedAmount(dto);
        MemberMasterAmountDto masterAmountDto = storeConverter.convert(masterUsableBalanceDto, usedAmountDto);
        MemberStoreAccountDto accountDto = new MemberStoreAccountDto();
        accountDto.setTotalAmountDto(totalAmountDto);
        accountDto.setMasterAmountDto(masterAmountDto);
        return accountDto;
    }
}
