package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值使用规则
 * @since 2024-11-14 14:31
 */
@Data
@Accessors(chain = true)
public class MemberStoreUsageRuleDto {

    /**
     * 使用规则模式
     * SINGLE 唯一
     * MULTIPLE 混合
     */
    private String ruleMode;

    /**
     * 规则ID
     */
    private String usageRuleId;
    /**
     * 储值使用模式
     * 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    private Integer usageMode;

    /**
     * 储值使用模式值
     */
    private List<MasterObject> usageModeScopes;


    /**
     * 平台适用范围值
     */
    private List<String> scopePlatformChannels;



    /**
     * 规则名称
     */
    private String usageRuleName;

    /**
     * 扣减类型
     */
    private Integer deductionType;

    /**
     * 扣减比例
     */
    private String deductionRatio;

    /**
     * 储值是否可使用
     */
    private Integer isCanUse;


}
