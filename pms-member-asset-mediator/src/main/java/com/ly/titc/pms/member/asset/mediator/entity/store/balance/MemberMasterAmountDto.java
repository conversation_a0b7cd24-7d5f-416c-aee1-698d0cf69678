package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员归属余额和已使用
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class MemberMasterAmountDto {

    /**
     * 充值使用类型
     */
    private Integer masterType;

    /**
     * 充值使用类型编码
     */
    private String masterCode;


    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 平台
     */
    private String platform;

    /**
     * 本店+平台渠道总剩余可用余额
     */
    private BigDecimal usableTotalBalance = BigDecimal.ZERO;
    /**
     * 本店+平台渠道总剩余可用本金
     */
    private BigDecimal usableTotalCapitalBalance = BigDecimal.ZERO;

    /**
     * 本店+平台渠道总剩余可用礼金
     */
    private BigDecimal usableTotalGiftBalance = BigDecimal.ZERO;

    /**
     * 本店总已使用（本店下的所有渠道）
     */
    private BigDecimal usedTotalAmount = BigDecimal.ZERO;

    /**
     * 本店总已使用本金（本店下的所有渠道）
     */
    private BigDecimal usedTotalCapitalBalance = BigDecimal.ZERO;

    /**
     * 本店总已使用礼金（本店下的所有渠道）
     */
    private BigDecimal usedTotalGiftAmount = BigDecimal.ZERO;

    /**
     * 可用的储值记录和对应规则
     */
    private List<MemberStoreRechargeRuleRecordDto> usableRecords;


}
