package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.dubbo.entity.request.ConsumeBizContentDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:06
 */
@Data
@Accessors(chain = true)
public class MemberMemberStoreConsumeDto extends GetMemberUsableDto {

    /**
     * 本次消费金额
     */
    private BigDecimal amount;

    /**
     * 消费类型：pay:支付，freeze冻结,
     */
    private String consumeType;

    /**
     * 消费描述
     */
    private String consumeDesc;

    /**
     * 业务消费号（订单号）(来源系统+业务消费号幂等)
     */
    private String bizConsumeNo;


    /**
     * 来源系统：会员-MEMBER，商品部-SHOP 餐饮-FOOD 微订房-WEBOOKNG 收银台——CASHIER
     */
    private String sourceSystem;


    /**
     * 业务内容
     */
    private ConsumeBizContentDto bizContentDto;

//    /**
//     * 业务线唯一号
//     */
//    private String businessNo;
//
//    /**
//     *  WXBOOKINGPAY(微订房支付) ,PMSPAY(PMS支付)
//     */
//    private String businessType;

//    /**
//     * 业务请求体
//     */
//    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

}
