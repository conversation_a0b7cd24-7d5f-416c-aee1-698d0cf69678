package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:06
 */
@Data
@Accessors(chain = true)
public class MemberMemberStoreConsumeDto extends GetMemberUsableDto {

    /**
     * 本次消费金额
     */
    private BigDecimal amount;

    /**
     * 消费类型：pay:支付，prePay预授权
     */
    private String consumeType;

    /**
     * 消费描述
     */
    private String consumeDesc;

    /**
     * 业务线唯一号
     */
    private String businessNo;

    /**
     *  WXBOOKINGPAY(微订房支付) ,PMSPAY(PMS支付)
     */
    private String businessType;

    /**
     * 业务请求体
     */
    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

}
