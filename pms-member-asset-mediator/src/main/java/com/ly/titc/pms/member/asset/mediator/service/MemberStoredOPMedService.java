package com.ly.titc.pms.member.asset.mediator.service;

import com.ly.titc.pms.member.asset.mediator.entity.store.*;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:51
 */
public interface MemberStoredOPMedService {

    /**
     * 保存充值记录
     * 使用充值交易号幂等
     */
    MemberStoreRecordOPResultDto recharge(MemberStoreRechargeDto dto);

    /**
     * 充值后回滚 （仅支持整单退款）
     */
    MemberStoreRecordOPResultDto rechargeRollback(MemberStoreRechargeRollBackDto dto);

    /**
     * 使用会员储值
     */
    MemberStoreRecordOPResultDto consumeStore(MemberMemberStoreConsumeDto dto);

    /**
     * 储值消费预计算
     */
    MemberStoreConsumeCalDto consumeStoreCal(MemberMemberStoreConsumeDto dto);

    /**
     * 储值资产冻结
     */
    MemberStoreRecordOPResultDto freeze(MemberMemberStoreFreezeDto dto);

    /**
     * 储值资产解冻
     */
    MemberStoreRecordOPResultDto unFreeze(UnfreezeConsumeRecordNoDto dto);



}
