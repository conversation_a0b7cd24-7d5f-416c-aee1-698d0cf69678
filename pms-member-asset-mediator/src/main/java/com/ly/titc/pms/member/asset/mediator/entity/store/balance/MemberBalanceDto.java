package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-9 19:52
 */
@Data
@Accessors(chain = true)
public class MemberBalanceDto {
    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance=BigDecimal.ZERO;

    /**
     * 总本金余额
     */
    private BigDecimal totalCapitalBalance =BigDecimal.ZERO;

    /**
     * 总赠送余额
     */
    private BigDecimal totalGiftBalance =BigDecimal.ZERO;
}
