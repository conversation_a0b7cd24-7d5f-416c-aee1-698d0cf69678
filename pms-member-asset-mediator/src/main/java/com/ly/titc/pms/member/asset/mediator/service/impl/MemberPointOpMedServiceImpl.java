package com.ly.titc.pms.member.asset.mediator.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.asset.biz.MemberPointConsumeRecordBiz;
import com.ly.titc.pms.member.asset.biz.MemberPointReceiveConsumeMappingBiz;
import com.ly.titc.pms.member.asset.biz.MemberPointReceiveRecordBiz;
import com.ly.titc.pms.member.asset.com.constant.SystemConstant;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.utils.CommonUtil;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointReceiveRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.asset.entity.MemberPointReceiveRecordWrapper;
import com.ly.titc.pms.member.asset.mediator.convert.MemberPointMedConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.*;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberMasterUsableScoresDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import com.ly.titc.pms.member.asset.mediator.handler.AssetCheckHandler;
import com.ly.titc.pms.member.asset.mediator.handler.MemberPointConsumeHandler;
import com.ly.titc.pms.member.asset.mediator.handler.MemberPointHandler;
import com.ly.titc.pms.member.asset.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.asset.mediator.service.MemberPointOpMedService;
import com.ly.titc.pms.member.asset.service.MemberPointRecordOpService;
import com.ly.titc.pms.member.asset.service.MemberPointRecordService;
import com.ly.titc.springboot.redisson.client.RedissonLockClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 16:51
 */
@Slf4j
@Service
public class MemberPointOpMedServiceImpl implements MemberPointOpMedService {
    @Resource(type = RedissonLockClient.class)
    private RedissonLockClient redissonLockClient;
    @Resource(type = MemberPointRecordOpService.class)
    private MemberPointRecordOpService recordOpService;
    @Resource(type = MemberPointMedConverter.class)
    private MemberPointMedConverter  converter;
    @Resource(type = MemberPointReceiveConsumeMappingBiz.class)
    private MemberPointReceiveConsumeMappingBiz consumeMappingBiz;
    @Resource(type = MemberPointHandler.class)
    private MemberPointHandler pointHandler;
    @Resource(type = MemberPointConsumeHandler.class)
    private MemberPointConsumeHandler consumeHandler;
    @Resource(type = AssetCheckHandler.class)
    private AssetCheckHandler assetCheckHandler;
    @Resource(type = MemberPointRecordService.class)
    private MemberPointRecordService recordService;
    @Resource
    private MemberPointConsumeRecordBiz consumeRecordBiz;
    @Resource
    private MemberPointReceiveRecordBiz receiveRecordBiz;
    @Resource
    private HotelDecorator hotelDecorator;

    @Override
    public MemberRecordOPResultDto receive(ReceiveMemberPointDto dto) {
        //1.校验参数
        assetCheckHandler.checkArgs(dto);
        //2.保存交易记录
        String lockKey = SystemConstant.POINT_LOCK+dto.getBusinessType()+dto.getBusinessNo();
        RLock rLock = redissonLockClient.lock(lockKey, 0L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getBusinessType()+dto.getBusinessNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "保存积分获取锁失败");
        }
        //校验幂等
        MemberPointReceiveRecord existInDB = recordOpService.getReceiveByBusinessNo(dto.getBusinessNo(), dto.getBusinessType(), dto.getMemberNo());

        if (existInDB != null) {
            log.error("交易号已存在{}", dto.getBusinessNo());
            throw new ServiceException(RespCodeEnum.CODE_1001);
        }
        MemberPointReceiveRecord receiveRecord =  converter.convert(dto);
        receiveRecord.setReceiveRecordNo(CommonUtil.generateUniqueNo(SystemConstant.POINT_NO_PREFIX));
        //计算余额
        receiveRecord.setBalanceScore(dto.getScore());
        MemberPointReceiveRecordWrapper wrapper = new MemberPointReceiveRecordWrapper();
        wrapper.setReceiveRecord(receiveRecord);
        MemberPointRecord record = converter.convert(receiveRecord);
        record.setActionType(receiveRecord.getActionType());
        record.setActionNo(receiveRecord.getReceiveRecordNo());
        wrapper.setPointRecord(record);
        recordOpService.saveReceive(wrapper);
        MemberRecordOPResultDto recordOPDto = new MemberRecordOPResultDto();
        recordOPDto.setRecordNo(receiveRecord.getReceiveRecordNo());
        return recordOPDto;
    }

    @Override
    public MemberRecordOPResultDto receiveRollback(ReceiveRollBackMemberPointDto dto) {
        //1.校验参数
        assetCheckHandler.checkArgs(dto);
        assetCheckHandler.checkBusinessNoAndRecordNo(dto.getBusinessType(),dto.getBusinessNo(),dto.getReceiverNo());
        //查询业务单号是否存在
        MemberPointReceiveRecord existInDB =receiveRecordBiz.getReceive(dto.getBusinessNo(),dto.getBusinessType(),dto.getReceiverNo(),dto.getMemberNo());
        if(existInDB == null){
            log.error("交易号不存在{}", JSON.toJSONString(dto));
            throw new ServiceException(RespCodeEnum.CODE_1002);
        }
        if(!existInDB.getMasterType().equals(dto.getMasterType()) || !existInDB.getMasterCode().equals(dto.getMasterCode())){
            log.error("不是当前归属，无权限操作{}", JSON.toJSONString(dto));
            throw new ServiceException(RespCodeEnum.CODE_1007);
        }
        //2.保存交易记录
        String lockKey = SystemConstant.POINT_LOCK+dto.getBusinessType()+dto.getBusinessNo();
        RLock rLock = redissonLockClient.lock(lockKey, 0L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getBusinessType()+dto.getBusinessNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "保存积分获取锁失败");
        }
        //2.回滚时校验是否已使用，已使用不允许撤回
        List<String>  consumeTypes = Arrays.asList(AssetConsumeTypeEnum.PAY.getType(), AssetConsumeTypeEnum.ADJUST.getType());
        List<MemberPointReceiveConsumeMapping> consumeMappings =  consumeMappingBiz.listByReceiveNo(existInDB.getReceiveRecordNo(),consumeTypes,dto.getMemberNo());
        if(consumeMappings.size()>0){
            log.error("交易号已使用{}", dto.getBusinessNo());
            throw new ServiceException(RespCodeEnum.CODE_1006);
        }
        // 3.回滚时校验余额是否小于0
       if(existInDB.getBalanceScore().compareTo(dto.getScore()) < 0) {
           log.error("积分余额不足{}", dto.getBusinessNo());
           throw new ServiceException(RespCodeEnum.CODE_1003);
       }
       //4.计算回滚后原单余额
        existInDB.setBalanceScore(existInDB.getBalanceScore()-(dto.getScore()));
        existInDB.setModifyUser(dto.getOperator());
        existInDB.setGmtModified(null);
       // 生成新的交易记录
        MemberPointReceiveRecord rollBackRecord = converter.convertRecord(existInDB, dto);
        MemberPointReceiveRecordWrapper wrapper = new MemberPointReceiveRecordWrapper();
        wrapper.setReceiveRecord(existInDB);
        wrapper.setPointRecord(converter.convert(rollBackRecord));
        wrapper.setRollBackRecord(rollBackRecord);
        recordOpService.saveReceiveRollBack(wrapper);
        MemberRecordOPResultDto recordOPDto = new MemberRecordOPResultDto();
        recordOPDto.setRecordNo(rollBackRecord.getReceiveRecordNo());
        return recordOPDto;
    }

    @Override
    public MemberRecordOPResultDto consume(ConsumeMemberPointDto dto) {
        //1.校验参数
        assetCheckHandler.checkArgs(dto);
        //2.保存交易记录
        String lockKey = SystemConstant.POINT_LOCK+dto.getMemberNo();
        RLock rLock = redissonLockClient.lock(lockKey, 0L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getMemberNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "保存积分获取锁失败");
        }
        Integer score =  dto.getScore();
        GetMemberUsableDto usableDto = converter.convert(dto);
        usableDto.setIsNeedUsage(true);
        MemberMasterUsableScoresDto usableScoresDto =  pointHandler.calculateBalancePoint(usableDto);
       Integer usableTotalScore =  usableScoresDto.getUsableTotalScore();
       if(usableTotalScore < score){
            log.error("积分余额不足{}", dto.getBusinessNo());
            throw new ServiceException(RespCodeEnum.CODE_2001);
       }
        return consumeHandler.consume(usableScoresDto.getUsableRecords(),dto);
    }

    @Override
    public MemberRecordOPResultDto consumeRollback(ConsumeRollBackMemberPointDto dto) {
        //1.校验参数
        assetCheckHandler.checkArgs(dto);
        assetCheckHandler.checkBusinessNoAndRecordNo(dto.getBusinessType(),dto.getBusinessNo(),dto.getConsumeNo());

        //查询业务单号是否存在
        List<String> consumeTypes = Arrays.asList(AssetConsumeTypeEnum.PAY.getType(), AssetConsumeTypeEnum.ADJUST.getType());
        MemberPointConsumeRecord existInDB =consumeRecordBiz.getConsume(dto.getBusinessNo(),dto.getBusinessType(),dto.getConsumeNo(),dto.getMemberNo(),consumeTypes);
        if(existInDB == null){
            log.error("交易号不存在{}", JSON.toJSONString(dto));
            throw new ServiceException(RespCodeEnum.CODE_1002);
        }
        if(!existInDB.getMasterType().equals(dto.getMasterType()) || !existInDB.getMasterCode().equals(dto.getMasterCode())){
            log.error("不是当前归属，无权限操作{}", JSON.toJSONString(dto));
            throw new ServiceException(RespCodeEnum.CODE_1007);
        }
        //2.保存交易记录
        String lockKey = SystemConstant.POINT_LOCK+dto.getBusinessType()+dto.getBusinessNo();
        RLock rLock = redissonLockClient.lock(lockKey, 0L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", dto.getMemberNo());
            throw new ServiceException(RespCodeEnum.CODE_500.getCode(), "保存积分获取锁失败");
        }
        //查询已退金额
        List<MemberPointConsumeRecord> refundsRecodes=  consumeRecordBiz.listConsumeByOriginalNo(existInDB.getConsumeRecordNo(),dto.getMemberNo());
        Integer totalRefundScore = refundsRecodes.stream().mapToInt(MemberPointConsumeRecord::getScore).sum();
        if(totalRefundScore + dto.getScore() > existInDB.getScore()){
            log.error("已退金额大于消费金额{}", dto.getBusinessNo());
            throw new ServiceException(RespCodeEnum.CODE_1005);
        }
        //根据消费记录号查询已使用的获得积分记录号 todo 需要保存已退金额
        List<MemberPointReceiveConsumeMapping> consumeMappings =  consumeMappingBiz.listByConsumeRecordNo(dto.getConsumeNo(),dto.getMemberNo(),0);
        //回滚余额 余额加回去 保存类型为退款的消费记录，保存积分记录
        //1.如果退款时已过期，立即过期
        //2.如果退款时未过期，余额加回去

        return null;
    }

    @Override
    public List<MemberPointDto> listConsumeRecords(ListMemberPointConsumeForBusinessDto dto) {
        assetCheckHandler.checkBusinessNoAndRecordNo(dto.getBusinessType(),dto.getBusinessNo(),dto.getReceiverNo());
        //查询获得积分的记录
        MemberPointReceiveRecord existInDB =receiveRecordBiz.getReceive(dto.getBusinessNo(),dto.getBusinessType(),dto.getReceiverNo(),dto.getMemberNo());
        if(existInDB == null){
            log.error("交易号不存在{}", JSON.toJSONString(dto));
            throw new ServiceException(RespCodeEnum.CODE_1002);
        }

        List<MemberPointReceiveConsumeMapping> consumeMappings =  consumeMappingBiz.listByReceiveRecordNo(dto.getReceiverNo(),dto.getMemberNo(),0);
        List<String> consumeNos = consumeMappings.stream().map(MemberPointReceiveConsumeMapping::getConsumeRecordNo).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(consumeNos)){
            return Collections.emptyList();
        }
        List<MemberPointRecord> records = recordService.listByRecordNos(dto.getMemberNo(), consumeNos);
        List<String> hotelCodes = records.stream().filter(item -> item.getMasterType().equals(MasterTypeEnum.HOTEL.getType()))
                .map(MemberPointRecord::getMasterCode).distinct().collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(hotelCodes);
        Map<String, String> hotelNameMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName));

        return records.stream().map(record -> converter.convertMemberPointDto(record, hotelNameMap.getOrDefault(record.getMasterCode(),  "集团"))).collect(Collectors.toList());
    }


}
