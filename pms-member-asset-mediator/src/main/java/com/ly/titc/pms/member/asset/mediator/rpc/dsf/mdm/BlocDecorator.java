package com.ly.titc.pms.member.asset.mediator.rpc.dsf.mdm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.mdm.api.BlocService;
import com.ly.titc.mdm.entity.request.bloc.GetByCodeReq;
import com.ly.titc.mdm.entity.request.bloc.GetDetailByCodeReq;
import com.ly.titc.mdm.entity.request.bloc.SaveBlocReq;
import com.ly.titc.mdm.entity.request.bloc.SelectBlocReq;
import com.ly.titc.mdm.entity.response.bloc.BlocDetailInfoResp;
import com.ly.titc.mdm.entity.response.bloc.BlocInfoResp;
import com.ly.titc.mdm.entity.response.bloc.SelectBlocResp;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;


/**
 * @Description: 酒店品牌
 * @Author: lixu
 * @Date: 2022/5/25
 */
@Slf4j
@Component
public class BlocDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private BlocService blocService;

    /**
     * 根据code获取用户信息
     * @param blocCode
     * @return
     */
    public BlocInfoResp getByCode(String blocCode) {
        GetByCodeReq req = new GetByCodeReq();
        req.setBlocCode(blocCode);
        req.setState(StatusEnum.VALID.getStatus());
        return Response.getValidateData(blocService.getByCode(req));
    }

    /**
     * 根据code获取明细
     * @param req
     * @return
     */
    public BlocDetailInfoResp getDetailByCode(GetDetailByCodeReq req) {
        return Response.getValidateData(blocService.getDetailByCode(req));
    }

    /**
     * 集团下拉列表
     *
     * @param req
     * @return
     */
    public List<SelectBlocResp> selectBloc(SelectBlocReq req) {

        return Response.getValidateData(blocService.selectBloc(req));
    }

    /**
     * 保存
     * @param req
     * @return
     */
    public Long save(SaveBlocReq req) {
        return Response.getValidateData(blocService.save(req));
    }
}
