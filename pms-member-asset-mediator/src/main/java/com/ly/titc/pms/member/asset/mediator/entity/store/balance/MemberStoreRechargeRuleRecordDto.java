package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 17:04
 */
@Data
@Accessors(chain = true)
public class MemberStoreRechargeRuleRecordDto {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 充值记录号
     */
    private String rechargeRecordNo;

    /**
     * 充值主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 充值主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 交易类型 pay:充值，refund：退款
     */
    private String tradeType;

    /**
     * 订单交易号（充值订单号，退款订单号）
     */
    private String tradeNo;

    /**
     * 平台
     */
    private String platformChannel;

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 本金余额
     */
    private BigDecimal capitalBalance;

    /**
     * 赠送余额
     */
    private BigDecimal giftBalance;

    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance;

    /**
     * 充值活动code
     */
    private String activityCode;

    /**
     * 赠送礼金过期时间
     */
    private String giftExpireDate;

    /**
     * 使用规则模式
     */
    private String usageRuleMode;

    /**
     * 使用规则ID
     */
    private String usageRuleId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;


    /**
     * 当前这条记录的储值使用规则
     */
    private MemberStoreUsageRuleDto usageRuleDto;

}
