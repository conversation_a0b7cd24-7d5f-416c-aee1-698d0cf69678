package com.ly.titc.pms.member.asset.mediator.entity.point;

import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-14 13:40
 */
@Data
@Accessors(chain = true)
public class MemberPointAccountDto {

    /**
     * 总可用，不包含指定平台和指定主体的账户积分信息
     */
    private MemberTotalPointDto totalPointDto;

    /**
     * 指定主体和平台账户积分
     */
    private MemberMasterPointDto masterPointDto;


}
