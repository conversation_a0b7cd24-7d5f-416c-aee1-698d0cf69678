package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员储值使用规则
 * @since 2024-11-14 14:31
 */
@Data
@Accessors(chain = true)
public class MemberStoreUsageMultiRuleDto extends MemberStoreUsageRuleDto{


    /**
     * 适用的门店编码
     */
    private List<String>  hotelCodes;

    /**
     * 适用的平台列表
     */
    private List<String> platforms;





}
