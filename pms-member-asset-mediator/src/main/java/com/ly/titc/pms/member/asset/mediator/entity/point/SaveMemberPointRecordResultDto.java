package com.ly.titc.pms.member.asset.mediator.entity.point;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 16:16
 */
@Data
@Accessors(chain = true)
public class SaveMemberPointRecordResultDto extends BaseMemberDto {

    /**
     * 记录号
     */
    private String recordNo;
    /**
     * 获得记录号
     */
    private String receiveNo;

    /**
     * 消费记录号
     */
    private String consumeNo;

}
