package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import com.ly.titc.pms.member.asset.dubbo.enums.StoreUseResultEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@Data
@Accessors(chain = true)
@Builder

public class MemberStoreBalanceResult {

    /**
     * 计算结果枚举
     */
    private StoreUseResultEnum result;

    /**
     * 失败原因
     */
    private String message;

    /**
     * 计算结果，成功时返回
     */
   private  MemberMasterUsableBalanceDto usableBalanceDto;

   public MemberStoreBalanceResult(StoreUseResultEnum result,String message) {
       this.result = result;
       this.message = message;
       this.usableBalanceDto = initUsableBalanceDto();
   }

    public MemberStoreBalanceResult(StoreUseResultEnum result,String message,MemberMasterUsableBalanceDto usableBalanceDto) {
        this.result = result;
        this.message = message;
        this.usableBalanceDto = usableBalanceDto;
    }


    public boolean isSuccess() {
        return StoreUseResultEnum.Success == this.result;
    }

    private MemberMasterUsableBalanceDto initUsableBalanceDto() {
        return new MemberMasterUsableBalanceDto()
                .setTotalBalance(BigDecimal.ZERO)
                .setTotalCapitalBalance(BigDecimal.ZERO)
                .setTotalGiftBalance(BigDecimal.ZERO)
                .setUsableTotalBalance(BigDecimal.ZERO)
                .setUsableTotalCapitalBalance(BigDecimal.ZERO)
                .setUsableTotalGiftBalance(BigDecimal.ZERO);

    }


}
