package com.ly.titc.pms.member.asset.mediator.handler;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberStoreDeductionTypeEnum;
import com.ly.titc.pms.member.asset.com.constant.SystemConstant;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.utils.CommonUtil;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeFreezeDetail;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.dubbo.enums.MemberStoreUsageRuleModeEnum;
import com.ly.titc.pms.member.asset.entity.MemberStoreConsumeWrapper;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberMemberStoreConsumeDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberMemberStoreFreezeDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStoreRecordOPResultDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeRecordService;
import com.ly.titc.pms.member.asset.service.MemberStoreRecordService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 16:21
 */
@Slf4j
@Component
public class MemberStoreConsumeHandler {
    @Resource
    private MemberStoreConverter converter;
    @Resource
    private MemberStoreRecordService storeRecordService;
    @Resource
    private MemberStoreRechargeConsumeRecordService consumeRecordService;

    /**
     * 消费储值卡
     * @param recordDtos
     */
    @Transactional(rollbackFor = Exception.class)
    public MemberStoreRecordOPResultDto consumeStore(List<MemberStoreRechargeRuleRecordDto> recordDtos, MemberMemberStoreConsumeDto dto){
        MemberStoreCalWrapper calWrapper = consumePreCal(recordDtos,dto);
        List<MemberStoreRechargeRecordBalanceDto> recordBalances = calWrapper.getCapitalBalances();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = calWrapper.getGiftBalances();
        //3.更新储值记录的余额
        List<MemberStoreRechargeRecord>  needUpdates= updateRechargeRecord(converter.convertDtoList(recordDtos),recordBalances,giftBalances,dto.getOperator());
        //4.保存消费记录
        return saveConsumeRecord(needUpdates,dto,calWrapper);
    }

    /**
     * 冻结
     */
    public MemberStoreRecordOPResultDto freeze(List<MemberStoreRechargeRuleRecordDto> recordDtoList, MemberMemberStoreFreezeDto dto){
        recordDtoList.sort(Comparator.comparing(MemberStoreRechargeRuleRecordDto::getGmtCreate));
        //需要消费的本金
        BigDecimal capitalAmount = dto.getCapitalAmount();
        BigDecimal giftAmount = dto.getGiftAmount();
        //消费多规则的模式优先使用 因为只有历史记录存在这种数据，所以先消费多规则
        List<MemberStoreRechargeRecordBalanceDto> capitalBalances = new ArrayList<>();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = new ArrayList<>();
        MemberStoreCalWrapper calWrapper = new MemberStoreCalWrapper();
        calWrapper.setCapitalBalances(capitalBalances);
        calWrapper.setGiftBalances(giftBalances);
        for(MemberStoreRechargeRuleRecordDto recordDto:recordDtoList){
            if(capitalAmount.equals(BigDecimal.ZERO) && giftAmount.equals(BigDecimal.ZERO)){
                break;
            }
            //1.扣减本金
            if(capitalAmount.compareTo(BigDecimal.ZERO)>0) {
                MemberStoreRechargeRecordBalanceDto balanceDto = consumeBalanceRecode(recordDto, capitalAmount);
                capitalAmount = balanceDto.getRemainAmount();
                capitalBalances.add(balanceDto);
            }
            //2.扣减礼金
            if(giftAmount.compareTo(BigDecimal.ZERO)>0) {
                MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = consumeGiftBalanceRecode(recordDto, giftAmount);
                giftAmount = giftBalanceDto.getRemainAmount();
                giftBalances.add(giftBalanceDto);
            }
        }

        //3.更新储值记录的余额
        List<MemberStoreRechargeRecord>  needUpdates= updateRechargeRecord(converter.convertDtoList(recordDtoList),capitalBalances,giftBalances,dto.getOperator());

        //4.保存消费记录
        return saveFreezeRecord(needUpdates,dto,calWrapper);
    }



    /**
     * 预计算
     */
    public MemberStoreCalWrapper consumePreCal(List<MemberStoreRechargeRuleRecordDto> recordDtoList, MemberMemberStoreConsumeDto dto){
        //根据创建时间升序排序，先消耗先充值的记录
        recordDtoList.sort(Comparator.comparing(MemberStoreRechargeRuleRecordDto::getGmtCreate));
        //获取所有的充值使用规则
        List<MemberStoreUsageRuleDto> usageRules =  recordDtoList.stream().map(MemberStoreRechargeRuleRecordDto::getUsageRuleDto).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        //获取单一模式的使用规则
        List<MemberStoreUsageRuleDto> singleUsageRules = usageRules.stream().filter(rule -> rule.getRuleMode().equals(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode())).collect(Collectors.toList());
        //获取混合模式的使用规则
        List<MemberStoreUsageRuleDto> multiUsageRules = usageRules.stream().filter(rule -> rule.getRuleMode().equals(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode())).collect(Collectors.toList());
        //records根据usageRuleId分组
        Map<String,List<MemberStoreRechargeRuleRecordDto>> recordMap = recordDtoList.stream().collect(Collectors.groupingBy(MemberStoreRechargeRuleRecordDto::getUsageRuleId));
        //消费多规则的模式优先使用 因为只有历史记录存在这种数据，所以先消费多规则
        List<MemberStoreRechargeRecordBalanceDto> capitalBalances = new ArrayList<>();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = new ArrayList<>();
        MemberStoreCalWrapper calWrapper = new MemberStoreCalWrapper();
        calWrapper.setCapitalBalances(capitalBalances);
        calWrapper.setGiftBalances(giftBalances);
        //1.扣减多规则储值记录
        BigDecimal remainAmount=  consumeMultiRule(multiUsageRules,recordMap,dto.getAmount(),calWrapper);
        //2.扣减单一模式规则储值记录
        remainAmount=  consumeRule(singleUsageRules.get(0),recordMap,remainAmount,calWrapper);
        if(remainAmount.compareTo(BigDecimal.ZERO)>0){
            throw new ServiceException(RespCodeEnum.CODE_1005);
        }
        return calWrapper;
    }
    private MemberStoreRecordOPResultDto saveFreezeRecord(List<MemberStoreRechargeRecord>  needUpdates, MemberMemberStoreFreezeDto dto, MemberStoreCalWrapper calWrapper){
        List<MemberStoreRechargeRecordBalanceDto> capitalBalances = calWrapper.getCapitalBalances();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = calWrapper.getGiftBalances();
        //.保存消费记录
        MemberStoreRechargeConsumeRecord consumeRecord = converter.convertConsumeRecord(dto);
        consumeRecord.setConsumeRecordNo(CommonUtil.generateUniqueNo(SystemConstant.RECHARGE_NO_PREFIX));
        consumeRecord.setCapitalAmount(capitalBalances.stream().map(MemberStoreRechargeRecordBalanceDto::getConsumeAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        consumeRecord.setGiftAmount(giftBalances.stream().map(MemberStoreRechargeRecordGiftBalanceDto::getConsumeAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        consumeRecord.setTotalAmount(dto.getCapitalAmount().add(dto.getGiftAmount()));
        Map<String,MemberStoreRechargeRecordBalanceDto> recordBalanceDtoMap = capitalBalances.stream().collect(Collectors.toMap(MemberStoreRechargeRecordBalanceDto::getRechargeRecordNo,record->record));
        Map<String,MemberStoreRechargeRecordGiftBalanceDto> giftBalanceDtoMap = giftBalances.stream().collect(Collectors.toMap(MemberStoreRechargeRecordGiftBalanceDto::getRechargeRecordNo,record->record));
        //.保存本次消费和储值记录的关联
        List<MemberStoreRechargeConsumeMapping> consumeMappings = new ArrayList<>();
        needUpdates.forEach(record -> {
            String rechargeRecordNo = record.getRechargeRecordNo();
            BigDecimal consumeAmount = BigDecimal.ZERO;
            BigDecimal consumeGiftAmount = BigDecimal.ZERO;
            MemberStoreRechargeRecordBalanceDto recordBalanceDto =  recordBalanceDtoMap.get(rechargeRecordNo);
            MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = giftBalanceDtoMap.get(rechargeRecordNo);
            MemberStoreRechargeConsumeMapping mapping = converter.convertMapping(dto);
            mapping.setRechargeRecordNo(record.getRechargeRecordNo());
            if(recordBalanceDto!=null){
                consumeAmount = recordBalanceDto.getConsumeAmount();
            }
            if(giftBalanceDto!=null){
                consumeGiftAmount = giftBalanceDto.getRemainGiftBalance();
            }
            BigDecimal totalConsumeAmount = consumeAmount.add(consumeGiftAmount);
            mapping.setConsumeCapitalAmount(consumeAmount);
            mapping.setConsumeGiftAmount(consumeGiftAmount);
            mapping.setTotalConsumeAmount(totalConsumeAmount);
            consumeMappings.add(mapping);
        });
        MemberStoreRechargeFreezeDetail freezeDetail = converter.convertFreeze(dto);
        freezeDetail.setConsumeRecordNo(consumeRecord.getConsumeRecordNo());
        MemberStoreConsumeWrapper wrapper = new MemberStoreConsumeWrapper();
        wrapper.setConsumeRecord(consumeRecord);
        wrapper.setConsumeMappings(consumeMappings);
        wrapper.setFreezeDetail(freezeDetail);
        consumeRecordService.saveFreezeRecord(wrapper);
        MemberStoreRecordOPResultDto resultDto = new MemberStoreRecordOPResultDto();
        resultDto.setMemberNo(dto.getMemberNo());
        resultDto.setRecodeNo(consumeRecord.getConsumeRecordNo());
        return resultDto;
    }


    private MemberStoreRecordOPResultDto saveConsumeRecord(List<MemberStoreRechargeRecord>  needUpdates, MemberMemberStoreConsumeDto dto, MemberStoreCalWrapper calWrapper){
        List<MemberStoreRechargeRecordBalanceDto> capitalBalances = calWrapper.getCapitalBalances();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = calWrapper.getGiftBalances();
        //.保存消费记录
        MemberStoreRechargeConsumeRecord consumeRecord = converter.convertConsumeRecord(dto);
        consumeRecord.setConsumeRecordNo(CommonUtil.generateUniqueNo(SystemConstant.RECHARGE_NO_PREFIX));
        consumeRecord.setCapitalAmount(capitalBalances.stream().map(MemberStoreRechargeRecordBalanceDto::getConsumeAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        consumeRecord.setGiftAmount(giftBalances.stream().map(MemberStoreRechargeRecordGiftBalanceDto::getConsumeAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
        consumeRecord.setTotalAmount(dto.getAmount());
        Map<String,MemberStoreRechargeRecordBalanceDto> recordBalanceDtoMap = capitalBalances.stream().collect(Collectors.toMap(MemberStoreRechargeRecordBalanceDto::getRechargeRecordNo,record->record));
        Map<String,MemberStoreRechargeRecordGiftBalanceDto> giftBalanceDtoMap = giftBalances.stream().collect(Collectors.toMap(MemberStoreRechargeRecordGiftBalanceDto::getRechargeRecordNo,record->record));
        //.保存本次消费和储值记录的关联
        List<MemberStoreRechargeConsumeMapping> consumeMappings = new ArrayList<>();
        needUpdates.forEach(record -> {
            String rechargeRecordNo = record.getRechargeRecordNo();
            BigDecimal consumeAmount = BigDecimal.ZERO;
            BigDecimal consumeGiftAmount = BigDecimal.ZERO;
            MemberStoreRechargeRecordBalanceDto recordBalanceDto =  recordBalanceDtoMap.get(rechargeRecordNo);
            MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = giftBalanceDtoMap.get(rechargeRecordNo);
            MemberStoreRechargeConsumeMapping mapping = converter.convertMapping(dto);
            mapping.setRechargeRecordNo(record.getRechargeRecordNo());
            if(recordBalanceDto!=null){
                consumeAmount = recordBalanceDto.getConsumeAmount();
            }
            if(giftBalanceDto!=null){
                consumeGiftAmount = giftBalanceDto.getRemainGiftBalance();
            }
            BigDecimal totalConsumeAmount = consumeAmount.add(consumeGiftAmount);
            mapping.setConsumeCapitalAmount(consumeAmount);
            mapping.setConsumeGiftAmount(consumeGiftAmount);
            mapping.setTotalConsumeAmount(totalConsumeAmount);
            consumeMappings.add(mapping);
        });
        MemberStoreConsumeWrapper wrapper = new MemberStoreConsumeWrapper();
        wrapper.setConsumeRecord(consumeRecord);
        wrapper.setConsumeMappings(consumeMappings);
        consumeRecordService.saveConsumeRecord(wrapper);
        MemberStoreRecordOPResultDto resultDto = new MemberStoreRecordOPResultDto();
        resultDto.setMemberNo(dto.getMemberNo());
        resultDto.setRecodeNo(consumeRecord.getConsumeRecordNo());
        return resultDto;
    }

    private List<MemberStoreRechargeRecord> updateRechargeRecord(List<MemberStoreRechargeRecord> records,
                                                                        List<MemberStoreRechargeRecordBalanceDto> recordBalances,
                                                                        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances,
                                                                        String  operator ){
        Map<String,MemberStoreRechargeRecordBalanceDto> recordBalanceDtoMap = recordBalances.stream().collect(Collectors.toMap(MemberStoreRechargeRecordBalanceDto::getRechargeRecordNo,record->record));
        Map<String,MemberStoreRechargeRecordGiftBalanceDto> giftBalanceDtoMap = giftBalances.stream().collect(Collectors.toMap(MemberStoreRechargeRecordGiftBalanceDto::getRechargeRecordNo,record->record));
        List<MemberStoreRechargeRecord> needUpdates = new ArrayList<>();
        for(MemberStoreRechargeRecord record:records){
            String rechargeRecordNo =record.getRechargeRecordNo();
            MemberStoreRechargeRecordBalanceDto recordBalanceDto = recordBalanceDtoMap.get(rechargeRecordNo);
            MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = giftBalanceDtoMap.get(rechargeRecordNo);
            //如果不为空则表示该条储值记录被消费了
            if(recordBalanceDto !=null || giftBalanceDto!=null){
                if(recordBalanceDto!=null){
                    record.setCapitalBalance(recordBalanceDto.getRemainBalance());
                }
                if(giftBalanceDto!=null){
                    record.setGiftBalance(giftBalanceDto.getRemainGiftBalance());
                }
                record.setTotalBalance(record.getCapitalBalance().add(record.getGiftBalance()));
                record.setModifyUser(operator);
                needUpdates.add(record);
            }
        }
        //更新储值记录
        needUpdates.forEach(record -> {
            storeRecordService.updateByBalance(record,record.getVersion());
        });

        return needUpdates;
    }

    /**
     * 消费多规则的模式
     * @param multiUsageRules
     * @param recordMap
     * @param amount
     * @return 剩余未扣金额
     */
    private BigDecimal consumeMultiRule(List<MemberStoreUsageRuleDto> multiUsageRules, Map<String, List<MemberStoreRechargeRuleRecordDto>> recordMap, BigDecimal amount,
                                        MemberStoreCalWrapper calWrapper) {
        //获取可使用的储值记录
        if(CollectionUtils.isEmpty(multiUsageRules)){
            return amount;
        }
        List<MemberStoreRechargeRuleRecordDto> multiRecords = new ArrayList<>();
        multiUsageRules.forEach(usageRuleDto -> {
            List<MemberStoreRechargeRuleRecordDto> ruleRecords =   recordMap.get(usageRuleDto.getUsageRuleId());
            multiRecords.addAll(ruleRecords);
        });
        if(CollectionUtils.isEmpty(multiRecords)){
            return amount;
        }
        //根据创建时间时间在前的排在前面
        multiRecords.sort(Comparator.comparing(MemberStoreRechargeRuleRecordDto::getGmtCreate));
        //逐条扣减
        List<MemberStoreRechargeRecordBalanceDto> recordBalances = calWrapper.getCapitalBalances();
        List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances = calWrapper.getGiftBalances();
        for(MemberStoreRechargeRuleRecordDto dto : multiRecords){
            if(amount.compareTo(BigDecimal.ZERO)<=0){
                break;
            }
            MemberStoreUsageRuleDto  ruleDto =  dto.getUsageRuleDto();
            Integer deductionType = ruleDto.getDeductionType();
            if(deductionType.equals(MemberStoreDeductionTypeEnum.BALANCE_FIRST.getValue())){
                //1.先扣本金
                MemberStoreRechargeRecordBalanceDto  balanceDto =   consumeBalanceRecode(dto,amount);
                recordBalances.add(balanceDto);
                amount = balanceDto.getRemainAmount();
                if(amount.compareTo(BigDecimal.ZERO)<=0){
                    break;
                }
                //2.再扣礼金
                MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = consumeGiftBalanceRecode(dto,amount);
                giftBalances.add(giftBalanceDto);
                amount = giftBalanceDto.getRemainAmount();

            }
            if(deductionType.equals(MemberStoreDeductionTypeEnum.GIFT_FIRST.getValue())){
                //1.扣减礼金
                MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = consumeGiftBalanceRecode(dto,amount);
                giftBalances.add(giftBalanceDto);
                amount = giftBalanceDto.getRemainAmount();
                if(amount.compareTo(BigDecimal.ZERO)<=0){
                    break;
                }
                //2.扣减本金
                MemberStoreRechargeRecordBalanceDto  balanceDto =   consumeBalanceRecode(dto,amount);
                recordBalances.add(balanceDto);
                amount = balanceDto.getRemainAmount();
            }
            if(deductionType.equals(MemberStoreDeductionTypeEnum.RATE.getValue())){
                //获取比例(30% 存储 30) 总消费100 余额70 礼金30
                String rate = ruleDto.getDeductionRatio();
                BigDecimal rateAmount = amount.multiply(new BigDecimal(rate)).divide(new BigDecimal(100));
                //1.本单扣减礼金
                MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = consumeGiftBalanceRecode(dto,rateAmount);
                giftBalances.add(giftBalanceDto);
                BigDecimal  remainRateAmount =   giftBalanceDto.getRemainAmount();
                //需要扣减的总本金
                amount = amount.subtract(rateAmount).add(remainRateAmount);
                //2.本单扣减本金
                MemberStoreRechargeRecordBalanceDto  balanceDto =   consumeBalanceRecode(dto,amount);
                recordBalances.add(balanceDto);
                amount = balanceDto.getRemainAmount();

            }

        }

        return amount;
    }

    /**
     * 消费规则下的储值记录
     * @param singleRule
     * @param recordMap
     * @param amount 消费金额
     */
    private BigDecimal consumeRule(MemberStoreUsageRuleDto singleRule,Map<String, List<MemberStoreRechargeRuleRecordDto>> recordMap, BigDecimal amount,
                                   MemberStoreCalWrapper calWrapper){
        if(singleRule ==null){
            return amount;
        }
        List<MemberStoreRechargeRuleRecordDto> recordDtoList =  recordMap.get(singleRule.getUsageRuleId());
        if(CollectionUtils.isEmpty(recordDtoList)){
            return amount;
        }
        //根据创建时间升序排序
        recordDtoList.sort(Comparator.comparing(MemberStoreRechargeRuleRecordDto::getGmtCreate));

        MemberStoreDeductionTypeEnum deductionTypeEnum = MemberStoreDeductionTypeEnum.getByValue(singleRule.getDeductionType());
        switch (deductionTypeEnum){
            case BALANCE_FIRST:
                //余额优先
                amount = consumeBalanceFirst(recordDtoList,calWrapper.getCapitalBalances(),amount);
                amount = consumeGiftFirst(recordDtoList,calWrapper.getGiftBalances(),amount);
                break;
            case GIFT_FIRST:
                amount = consumeGiftFirst(recordDtoList,calWrapper.getGiftBalances(),amount);
                amount = consumeBalanceFirst(recordDtoList,calWrapper.getCapitalBalances(),amount);
                break;
            case RATE:
                //获取比例(30% 存储 30) 总消费100 余额70 礼金30
                String rate = singleRule.getDeductionRatio();
                BigDecimal rateAmount = amount.multiply(new BigDecimal(rate)).divide(new BigDecimal(100));
                //先扣减礼金，礼金扣减完再扣减余额
               BigDecimal  remainRateAmount = consumeGiftFirst(recordDtoList,calWrapper.getGiftBalances(),rateAmount);
               //剩余待扣 = 总消费-礼金比例占用+礼金扣减后剩余
               amount = amount.subtract(rateAmount).add(remainRateAmount);
               amount=  consumeBalanceFirst(recordDtoList,calWrapper.getCapitalBalances(),amount);

                break;
        }
        return amount;
    }

    private BigDecimal consumeBalanceFirst(List<MemberStoreRechargeRuleRecordDto> recordDtoList,List<MemberStoreRechargeRecordBalanceDto> recordBalances,BigDecimal amount){
        for(MemberStoreRechargeRuleRecordDto record : recordDtoList){
            if(amount.compareTo(BigDecimal.ZERO)<=0){
                break;
            }
            MemberStoreRechargeRecordBalanceDto balanceDto = consumeBalanceRecode(record,amount);

            amount = balanceDto.getRemainAmount();
            recordBalances.add(balanceDto);
        }
        return amount;
    }

    private BigDecimal consumeGiftFirst(List<MemberStoreRechargeRuleRecordDto> recordDtoList,List<MemberStoreRechargeRecordGiftBalanceDto> giftBalances,BigDecimal amount){

        for(MemberStoreRechargeRuleRecordDto record : recordDtoList){
            if(amount.compareTo(BigDecimal.ZERO)<=0){
                break;
            }
            //判断是否过期
            if (StringUtils.isNotEmpty(record.getGiftExpireDate() )&& LocalDateUtil.parseByNormalDate(record.getGiftExpireDate()).isBefore(LocalDateUtil.now())) {
                log.info("该条充值记录礼金已过期：memberNo:{},rechargeRecordNo{}",record.getMemberNo(),record.getRechargeRecordNo());
                continue;
            }
            MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = consumeGiftBalanceRecode(record,amount);

            amount = giftBalanceDto.getRemainAmount();
            giftBalances.add(giftBalanceDto);
        }
        return amount;
    }

    /**
     * 余额扣减
     */

    private MemberStoreRechargeRecordBalanceDto consumeBalanceRecode(MemberStoreRechargeRuleRecordDto recordDto, BigDecimal amount){
        BigDecimal capitalBalance = recordDto.getCapitalBalance();
        RemainClass remainObj = calRemainBalance(capitalBalance,amount);
        //设置当前剩余
        MemberStoreRechargeRecordBalanceDto balanceDto = new MemberStoreRechargeRecordBalanceDto();
        balanceDto.setRechargeRecordNo(recordDto.getRechargeRecordNo());
        balanceDto.setConsumeAmount(remainObj.getConsumeAmount());
        balanceDto.setRemainBalance(remainObj.getRemainBalance());
        balanceDto.setRemainAmount(remainObj.getRemainAmount());
        balanceDto.setRechargeRecordNo(recordDto.getRechargeRecordNo());
        return balanceDto;
    }

    /**
     * 礼金扣减
     */
    private MemberStoreRechargeRecordGiftBalanceDto consumeGiftBalanceRecode(MemberStoreRechargeRuleRecordDto recordDto, BigDecimal amount){
        BigDecimal balance = recordDto.getGiftBalance();
        RemainClass remainObj = calRemainBalance(balance,amount);
        //设置当前剩余
        MemberStoreRechargeRecordGiftBalanceDto giftBalanceDto = new MemberStoreRechargeRecordGiftBalanceDto();
        giftBalanceDto.setRechargeRecordNo(recordDto.getRechargeRecordNo());
        giftBalanceDto.setConsumeAmount(remainObj.getConsumeAmount());
        giftBalanceDto.setRemainGiftBalance(remainObj.getRemainBalance());
        giftBalanceDto.setRemainAmount(remainObj.getRemainAmount());
        return giftBalanceDto;
    }

    private RemainClass calRemainBalance(BigDecimal balance,BigDecimal amount){
        RemainClass remainObj = new RemainClass();
        BigDecimal remainBalance = BigDecimal.ZERO;
        BigDecimal remainAmount = BigDecimal.ZERO;
        BigDecimal consumeAmount = BigDecimal.ZERO;
        if(balance.compareTo(amount)>=0){
            remainBalance =  balance.subtract(amount);
            consumeAmount = amount;
        }else{
            remainAmount = amount.subtract(balance);
            consumeAmount = balance;
        }
        remainObj.setRemainBalance(remainBalance);
        remainObj.setRemainAmount(remainAmount);
        remainObj.setConsumeAmount(consumeAmount);
        return remainObj;
    }

    @Getter
    @Setter
    static class RemainClass{
        /**
         * 剩余余额
         */
        private BigDecimal remainBalance;
        /**
         * 剩余待扣
         */
        private BigDecimal remainAmount;

        /**
         * 本次消费
         */
        private BigDecimal consumeAmount;
    }


}
