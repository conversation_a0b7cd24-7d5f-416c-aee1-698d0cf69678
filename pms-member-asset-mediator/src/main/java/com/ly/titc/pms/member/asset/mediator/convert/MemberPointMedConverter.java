package com.ly.titc.pms.member.asset.mediator.convert;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleAssetResp;
import com.ly.titc.pms.member.asset.dal.entity.bo.PageMemberPointBo;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedPointDto;
import com.ly.titc.pms.member.asset.dal.entity.po.*;
import com.ly.titc.pms.member.asset.dubbo.enums.PonitReceiveTypeEnum;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.*;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberMasterUsableScoresDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberPointReceiveRecordBalanceDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberPointRecordRuleRecordDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberPointUsageRuleDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreRechargeRuleRecordDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberPointConverter
 * @Date：2024-12-9 11:50
 * @Filename：MemberPointConverter
 */
@Mapper(componentModel = "spring")
public interface MemberPointMedConverter {

    MemberPointDto convertMemberPointDto(MemberPointRecord record, String masterName);

    MemberTotalPointDto convert(MemberPointAccountInfo accountInfo);

    MemberPointRecordRuleRecordDto convertRecord(MemberPointRecord record);
    @Mappings({
            @Mapping(target = "masterType",source = "usableScoresDto.masterType"),
            @Mapping(target = "masterCode",source = "usableScoresDto.masterCode")
    })
    MemberMasterPointDto convert(MemberMasterUsableScoresDto usableScoresDto, MemberUsedPointDto usedPointDto);

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")
    })
    MemberPointReceiveRecord convert(ReceiveMemberPointDto dto);

    @Mappings({
            @Mapping(target = "recordNo",source = "receiveRecordNo"),
            @Mapping(target = "tradeType",source = "receiveType"),
            @Mapping(target = "actionNo",source = "receiveRecordNo"),
    })
    MemberPointRecord convert(MemberPointReceiveRecord record);

    default MemberPointReceiveRecord convertRecord(MemberPointReceiveRecord receiveRecord, ReceiveRollBackMemberPointDto dto){
        MemberPointReceiveRecord rollBackRecord=  convertRecordBase(receiveRecord);
        rollBackRecord.setCreatetime(null);
        rollBackRecord.setUpdatetime(null);
        rollBackRecord.setGmtCreate(null);
        rollBackRecord.setGmtModified(null);
        rollBackRecord.setScore(-dto.getScore());
        rollBackRecord.setBalanceScore(0);
        rollBackRecord.setCreateUser(dto.getOperator());
        rollBackRecord.setModifyUser(dto.getOperator());
        rollBackRecord.setRemark(dto.getRemark());
        //todo 字段名称调整
        rollBackRecord.setOriginalConsumeRecordNo(receiveRecord.getReceiveRecordNo());
        rollBackRecord.setReceiveType(PonitReceiveTypeEnum.RECOVERY.getType());
        return rollBackRecord;
    }

    MemberPointReceiveRecord convertRecordBase(MemberPointReceiveRecord receiveRecord);

    GetMemberUsableDto convert(ConsumeMemberPointDto dto);

    MemberPointRecordRuleRecordDto convertRecordDto(MemberPointReceiveRecord receiveRecord);

    List<MemberPointUsageRuleDto> convert( List<MemberPointUsageRuleAssetResp> ruleAssetResps);

    @Mappings({
            @Mapping(target = "createUser",source = "operator"),
            @Mapping(target = "modifyUser",source = "operator")

    })
    MemberPointConsumeRecord convertConsumeRecord(ConsumeMemberPointDto dto);

    MemberPointReceiveConsumeMapping convert(MemberPointConsumeRecord consumeRecord);
    @Mappings({
            @Mapping(target = "recordNo",source = "consumeRecordNo"),
            @Mapping(target = "tradeType",source = "consumeType"),
    })
    MemberPointRecord convertPoint(MemberPointConsumeRecord consumeRecord);

    PageMemberPointBo convert(PageMemberPointDto dto);
}
