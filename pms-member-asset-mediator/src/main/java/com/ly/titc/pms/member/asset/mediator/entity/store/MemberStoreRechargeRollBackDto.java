package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:55
 */
@Data
@Accessors
public class MemberStoreRechargeRollBackDto extends BaseMemberDto {


    /**
     * 交易类型 pay:充值，refund：退款
     */
    private String tradeType;

    /**
     * 订单交易号（充值订单号）
     */
    private String tradeNo;

//    /**
//     * 原始充值交易号（退款时必传）
//     */
//    private String originalTradeNo;

    /**
     * 备注
     */
    private String remark;


}
