package com.ly.titc.pms.member.asset.mediator.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.asset.biz.MemberPointAccountBiz;
import com.ly.titc.pms.member.asset.com.utils.CommonUtil;
import com.ly.titc.pms.member.asset.dal.entity.bo.PageMemberPointBo;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedPointDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointRecord;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionItemEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.TradeOpetatorTypeEnum;
import com.ly.titc.pms.member.asset.mediator.convert.MemberPointMedConverter;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberMasterPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberPointAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberTotalPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.PageMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.balance.MemberMasterUsableScoresDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import com.ly.titc.pms.member.asset.mediator.handler.MemberPointHandler;
import com.ly.titc.pms.member.asset.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.asset.mediator.service.MemberPointRecordMedService;
import com.ly.titc.pms.member.asset.service.MemberPointRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberPointMedServiceImpl
 * @Date：2024-12-9 11:42
 * @Filename：MemberPointMedServiceImpl
 */
@Slf4j
@Service
public class MemberPointRecordMedServiceImpl implements MemberPointRecordMedService {

    @Resource
    private MemberPointRecordService service;
    @Resource
    private MemberPointMedConverter converter;

    @Resource
    private MemberStoreConverter storeConverter;
    @Resource
    private MemberPointAccountBiz pointAccountBiz;
    @Resource
    private MemberPointHandler pointHandler;

    @Resource
    private HotelDecorator hotelDecorator;

    @Override
    public Pageable<MemberPointDto> pageMemberPoint(PageMemberPointDto dto) {
        PageMemberPointBo bo = converter.convert(dto);
        Page<MemberPointRecord> page = service.pageMemberPointRecord(bo);
        List<MemberPointRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        List<String> hotelCodes = records.stream().filter(item -> item.getMasterType().equals(MasterTypeEnum.HOTEL.getType()))
                .map(MemberPointRecord::getMasterCode).distinct().collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(hotelCodes);
        Map<String, String> hotelNameMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName));


        return PageableUtil.convert(page, records.stream()
                .map(record -> {
                    MemberPointDto memberPointDto = converter.convertMemberPointDto(record, hotelNameMap.getOrDefault(record.getMasterCode(), "集团"));
                    memberPointDto.setCreateTime(CommonUtil.timestampToString(record.getCreateTime()));
                    memberPointDto.setUpdateTime(CommonUtil.timestampToString(record.getUpdateTime()));
                    if (StringUtils.isNotEmpty(record.getTradeType())) {
                        TradeOpetatorTypeEnum tradeOpetatorTypeEnum = TradeOpetatorTypeEnum.getByType(record.getTradeType());
                        if (Objects.nonNull(tradeOpetatorTypeEnum)) {
                            memberPointDto.setReason(tradeOpetatorTypeEnum.getDesc());
                        }
                    }
                    memberPointDto.setActionItemName(Arrays.stream(PointActionItemEnum.values())
                            .filter(it -> Objects.equals(it.getActionItem(), record.getActionItem())).findFirst().map(PointActionItemEnum::getActionItemDesc).orElse(""));
                    return memberPointDto;
                }).collect(Collectors.toList()));
    }

    @Override
    public MemberTotalPointDto getTotalAccountPoints(BaseMemberDto dto) {
        MemberPointAccountInfo accountInfo =  pointAccountBiz.getByMemberNo(dto.getMemberNo());
        MemberTotalPointDto totalPointDto=  converter.convert(accountInfo);
        if(totalPointDto == null){
            return new MemberTotalPointDto();
        }else{
            return totalPointDto;
        }
    }

    @Override
    public MemberPointAccountDto getUsableMasterAccount(GetMemberUsableDto dto) {
        BaseMemberDto baseMemberDto = storeConverter.convert(dto);
        //获取总积分
        MemberTotalPointDto totalPointDto = this.getTotalAccountPoints(baseMemberDto);
        //获取归属主体和平台的可用积分
        MemberMasterUsableScoresDto usableScoresDto =  pointHandler.calculateBalancePoint(dto);
        //获取归属主体的总使用积分
        MemberUsedPointDto usedPointDto =  pointHandler.calculateUsedPoint(dto);
        MemberMasterPointDto masterPointDto =  converter.convert(usableScoresDto,usedPointDto);
        MemberPointAccountDto accountDto =  new MemberPointAccountDto();
        accountDto.setMasterPointDto(masterPointDto);
        accountDto.setTotalPointDto(totalPointDto);
        return accountDto;
    }
}
