package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-18 20:25
 */
@Data
@Accessors
public class MemberStoreRechargeRecordGiftBalanceDto {

    /**
     * 充值记录号
     */
    private String rechargeRecordNo;

    /**
     * 本金余额
     */
    private BigDecimal remainGiftBalance;

    /**
     * 本次消费
     */
    private BigDecimal consumeAmount;

    /**
     * 剩余待扣减
     */
    private BigDecimal remainAmount;
}
