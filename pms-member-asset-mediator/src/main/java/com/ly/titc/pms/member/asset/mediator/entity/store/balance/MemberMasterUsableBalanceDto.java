package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:55
 */
@Data
@Accessors(chain = true)
public class MemberMasterUsableBalanceDto {

    /**
     * 充值使用类型
     */
    private Integer masterType;

    /**
     * 充值使用类型编码
     */
    private String masterCode;

    /**
     * 平台渠道
     */
    private String platformChannel;
    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance=BigDecimal.ZERO;

    /**
     * 总本金余额
     */
    private BigDecimal totalCapitalBalance =BigDecimal.ZERO;

    /**
     * 总赠送余额
     */
    private BigDecimal totalGiftBalance =BigDecimal.ZERO;

    /**
     * 本店+平台渠道总剩余可用余额
     */
    private BigDecimal usableTotalBalance = BigDecimal.ZERO;
    /**
     * 本店+平台渠道总剩余可用本金
     */
    private BigDecimal usableTotalCapitalBalance = BigDecimal.ZERO;

    /**
     * 本店+平台渠道总剩余可用礼金
     */
    private BigDecimal usableTotalGiftBalance = BigDecimal.ZERO;

    /**
     * 可用的储值记录和对应规则
     */
    private List<MemberStoreRechargeRuleRecordDto> usableRecords;


}
