package com.ly.titc.pms.member.asset.mediator.entity.point;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition  会员总积分信息
 * @since 2024-12-10 19:29
 */
@Data
@Accessors(chain = true)
public class MemberTotalPointDto {
    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 总积分
     */
    private Integer totalScore =0;

    /**
     * 总积分余额
     */
    private Integer totalScoreBalance=0;

    /**
     * 总已过期积分
     */
    private Integer totalExpireScore=0;

    /**
     * 总已使用积分
     */
    private Integer totalUsedScore=0;
}
