package com.ly.titc.pms.member.asset.mediator.service;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberTotalAmountDto;

/**
 * 会员储值Med服务
 *
 * <AUTHOR>
 * @date 2024/11/7 16:18
 */
public interface MemberStoredMedService {
    /**
     * 查询会员的总可用资产
     * 不区分平台和使用方
     */
    MemberTotalAmountDto getTotalAccountAmount(BaseMemberDto dto);

    /**
     * 查询会员可用储值账户
     */
    MemberStoreAccountDto getUsableMasterAccount(GetMemberUsableDto reqDto);
}
