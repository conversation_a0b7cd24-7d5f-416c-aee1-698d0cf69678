package com.ly.titc.pms.member.asset.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberPointAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberTotalPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.PageMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;

/**
 * @Author：rui
 * @name：MemberPointMedService
 * @Date：2024-12-9 11:40
 * @Filename：MemberPointMedService
 */
public interface MemberPointRecordMedService {

    Pageable<MemberPointDto> pageMemberPoint(PageMemberPointDto dto);


    /**
     * 查询总积分
     */
    MemberTotalPointDto getTotalAccountPoints(BaseMemberDto dto);


    /**
     * 查询可用积分
     */
    MemberPointAccountDto getUsableMasterAccount(GetMemberUsableDto dto);

}


