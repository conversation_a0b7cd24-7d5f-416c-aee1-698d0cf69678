package com.ly.titc.pms.member.asset.mediator.handler;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.biz.MemberStoreRechargeConsumeMappingBiz;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeRecord;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.entity.MemberStoreConsumeRefundWrapper;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStoreConsumeRollBackDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStoreConsumeRollBackResultDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStoreRecordOPResultDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreRefundContext;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeRecordService;
import com.ly.titc.pms.member.asset.service.MemberStoreRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.watcher.shaded.com.google.common.collect.Comparators.min;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Slf4j
@Component
public class MemberStoreRefundHandler {

    @Resource
    private MemberStoreRechargeConsumeRecordService consumeRecordService;
    @Resource
    private MemberStoreRechargeConsumeMappingBiz consumeMappingBiz;
    @Resource
    private MemberStoreConverter storeConverter;

    @Resource
    private MemberStoreRecordService storeRecordService;

   public MemberStoreConsumeRollBackResultDto consumeRollback(MemberStoreConsumeRollBackDto dto) {

       MemberStoreRechargeConsumeRecord originalRecord = consumeRecordService.getByRecordNo(dto.getMemberNo(), dto.getConsumeNo());
       if(originalRecord ==null){
           throw new ServiceException(RespCodeEnum.Origin_Record_Not_Exist);
       }
           // 3. 检查退款金额是否合法
       BigDecimal refundAmount = dto.getAmount();
       if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
           throw new ServiceException("退款金额必须大于0",RespCodeEnum.CODE_400.getCode());
       }

       if (refundAmount.compareTo(originalRecord.getTotalAmount()) > 0) {
           throw new ServiceException( "退款金额不能超过原始消费金额",RespCodeEnum.CODE_400.getCode());
       }

       // 4. 检查是否已全额退款
       if (originalRecord.getRefundTotalAmount() != null &&
               originalRecord.getRefundTotalAmount().compareTo(originalRecord.getTotalAmount()) >= 0) {
           throw new ServiceException( "该消费记录已全额退款",RespCodeEnum.CODE_400.getCode());
       }
       // 5. 计算本次可退款金额（考虑已退款部分）
       BigDecimal availableRefundAmount = originalRecord.getTotalAmount()
               .subtract(originalRecord.getRefundTotalAmount() == null ? BigDecimal.ZERO : originalRecord.getRefundTotalAmount());

       if (refundAmount.compareTo(availableRefundAmount) > 0) {
           throw new ServiceException("本次退款金额超过剩余可退款金额: " + availableRefundAmount.toPlainString(), RespCodeEnum.CODE_400.getCode());
       }

       // 6. 获取原始消费的礼金和本金金额
       BigDecimal originalGiftAmount = originalRecord.getGiftAmount() == null ?
               BigDecimal.ZERO : originalRecord.getGiftAmount();
       BigDecimal originalCapitalAmount = originalRecord.getCapitalAmount() == null ?
               BigDecimal.ZERO : originalRecord.getCapitalAmount();

       // 校验消费金额组成
       if (originalGiftAmount.add(originalCapitalAmount).compareTo(originalRecord.getTotalAmount()) != 0) {
           throw new ServiceException("原始消费记录金额组成异常",RespCodeEnum.CODE_400.getCode());
       }

       // 6. 按比例计算本次退款中礼金和本金的退还金额
       BigDecimal refundGiftAmount = calculateProportionalRefund(
               refundAmount, originalGiftAmount, originalRecord.getTotalAmount());

       BigDecimal refundCapitalAmount = calculateProportionalRefund(
               refundAmount, originalCapitalAmount, originalRecord.getTotalAmount());
       // 7. 创建退款记录
       MemberStoreRechargeConsumeRecord refundRecord = storeConverter.buildRefundRecord(dto, originalRecord);
       refundRecord.setCapitalAmount(refundCapitalAmount);
       refundRecord.setTotalAmount(refundAmount);
       refundRecord.setGiftAmount(refundGiftAmount);
       // 8. 执行按比例退款
       MemberStoreRefundContext context = prepareContext(dto);
       executeProportionalRefund( refundGiftAmount, refundCapitalAmount, context);
       //9.执行数据更新wrapper
       MemberStoreConsumeRefundWrapper wrapper = buildRefundWrapper(context, refundRecord,originalRecord);
       consumeRecordService.saveConsumeRefundRecord(wrapper);
       // 12. 返回退款结果
       return storeConverter.convertRefund(refundRecord);
   }

    private MemberStoreConsumeRefundWrapper buildRefundWrapper(MemberStoreRefundContext context, MemberStoreRechargeConsumeRecord refundRecord, MemberStoreRechargeConsumeRecord originalRecord) {
        BigDecimal totalAmount = refundRecord.getTotalAmount();
        BigDecimal giftAmount = refundRecord.getGiftAmount() == null ? BigDecimal.ZERO : refundRecord.getGiftAmount();
        BigDecimal capitalAmount = refundRecord.getCapitalAmount() == null ? BigDecimal.ZERO : refundRecord.getCapitalAmount();
        originalRecord.setRefundTotalAmount(originalRecord.getRefundTotalAmount() == null ? BigDecimal.ZERO.add(totalAmount) : originalRecord.getRefundTotalAmount().add(totalAmount));
        originalRecord.setRefundGiftAmount(originalRecord.getRefundGiftAmount() == null ? BigDecimal.ZERO.add(giftAmount) : originalRecord.getRefundGiftAmount().add(giftAmount));
        originalRecord.setRefundCapitalAmount(originalRecord.getRefundCapitalAmount() == null ? BigDecimal.ZERO.add(capitalAmount) : originalRecord.getRefundCapitalAmount().add(capitalAmount));
       return  MemberStoreConsumeRefundWrapper.builder()
               .refundRecord(refundRecord)
               .originalRecord(originalRecord)
               .needUpdateConsumeMappings(context.getNeedUpdateConsumeMappings())
               .needUpdateRechargeRecords(context.getNeedUpdateRechargeRecords())
               .build();
   }

    private MemberStoreRefundContext prepareContext(MemberStoreConsumeRollBackDto dto){
       MemberStoreRefundContext context = new MemberStoreRefundContext();
       List<MemberStoreRechargeConsumeMapping> originalMappings= consumeMappingBiz.listByConsumeRecordNo(dto.getMemberNo(), dto.getConsumeNo());
       List<String>  rechargeRecordNos = originalMappings.stream().map(MemberStoreRechargeConsumeMapping::getRechargeRecordNo).collect(Collectors.toList());
       List<MemberStoreRechargeRecord> rechargeRecords = storeRecordService.listRechargeRecords(rechargeRecordNos,dto.getMemberNo());
       context.setOriginalMappings(originalMappings);
       context.setRechargeRecordMap(rechargeRecords.stream().collect(Collectors.toMap(MemberStoreRechargeRecord::getRechargeRecordNo, Function.identity())));
       context.setNeedUpdateConsumeMappings(new ArrayList<>());
       context.setNeedUpdateRechargeRecords(new ArrayList<>());
       return context;
   }


    /**
     * 按比例计算退款金额
     */
    private BigDecimal calculateProportionalRefund(BigDecimal totalRefund,
                                                   BigDecimal originalComponent,
                                                   BigDecimal originalTotal) {
        if (originalTotal.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        // 计算比例: (原始部分金额 / 原始总金额) * 退款总额
        return originalComponent
                .multiply(totalRefund)
                .divide(originalTotal, 2, RoundingMode.HALF_UP);
    }

    /**
     * 执行按比例退款
     */
    private void executeProportionalRefund(BigDecimal refundGiftAmount,
                                           BigDecimal refundCapitalAmount,
                                           MemberStoreRefundContext refundContext) {

        Map<String, MemberStoreRechargeRecord> recordMap = refundContext.getRechargeRecordMap();
        List<MemberStoreRechargeConsumeMapping> mappings = refundContext.getOriginalMappings();
        // 1. 按消费明细退还礼金
        for (MemberStoreRechargeConsumeMapping mapping : mappings) {
            if (refundGiftAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            // 获取该明细的礼金消费金额
            BigDecimal giftConsumed = mapping.getConsumeGiftAmount() == null ?
                    BigDecimal.ZERO : mapping.getConsumeGiftAmount();
            if(giftConsumed.compareTo(BigDecimal.ZERO) <= 0){
                continue;
            }
            // 计算该明细可退还的礼金（考虑已退款部分）
            BigDecimal maxGiftRefund = calculateMaxRefundableAmount(mapping, true);
            if (maxGiftRefund.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 按比例分配退款金额（如果有多条明细）
            BigDecimal refundThisTime = min(refundGiftAmount, maxGiftRefund);

            // 退还礼金，更新上下文中的储值记录和已退款金额
            MemberStoreRechargeRecord record = recordMap.get(mapping.getRechargeRecordNo());
            if(record==null || record.getIsRefund()==1){
                throw new ServiceException("该储值记录已全额退款或不存在",RespCodeEnum.CODE_400.getCode());
            }
            refundGift(mapping,record, refundThisTime,refundContext);
            refundGiftAmount = refundGiftAmount.subtract(refundThisTime);

        }

        // 2. 按消费明细退还本金
        for (MemberStoreRechargeConsumeMapping mapping : mappings) {
            if (refundCapitalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            // 获取该明细的本金消费金额
            BigDecimal capitalConsumed = mapping.getConsumeCapitalAmount() == null ?
                    BigDecimal.ZERO : mapping.getConsumeCapitalAmount();
            if (capitalConsumed.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算该明细可退还的本金（考虑已退款部分）
            BigDecimal maxCapitalRefund = calculateMaxRefundableAmount(mapping, false);
            if (maxCapitalRefund.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 按比例分配退款金额
            BigDecimal refundThisTime = min(refundCapitalAmount, maxCapitalRefund);

            // 退还本金
            MemberStoreRechargeRecord record = recordMap.get(mapping.getRechargeRecordNo());
            if(record==null || record.getIsRefund()==1){
                throw new ServiceException("该储值记录已全额退款或不存在",RespCodeEnum.CODE_400.getCode());
            }
            refundCapital(mapping,record, refundThisTime,refundContext);
            refundCapitalAmount = refundCapitalAmount.subtract(refundThisTime);

        }
        if(refundGiftAmount.compareTo(BigDecimal.ZERO) > 0 || refundCapitalAmount.compareTo(BigDecimal.ZERO) > 0) {
            throw new ServiceException("退款金额分配异常", RespCodeEnum.CODE_400.getCode());
        }
    }

    /**
     * 退还礼金（更新上下文而不是直接更新数据库）
     */
    private void refundGift(MemberStoreRechargeConsumeMapping mapping,MemberStoreRechargeRecord record, BigDecimal refundAmount,
                            MemberStoreRefundContext context) {
        // 增加礼金余额
        record.setGiftBalance(record.getGiftBalance().add(refundAmount));
        mapping.setRefundGiftAmount(mapping.getRefundGiftAmount().add(refundAmount));
        mapping.setTotalRefundAmount(mapping.getTotalRefundAmount().add(refundAmount));

        // 添加到批量更新上下文
        context.getNeedUpdateRechargeRecords().add(record);
        context.getNeedUpdateConsumeMappings().add(mapping);
    }

    /**
     * 退还本金（更新上下文而不是直接更新数据库）
     */
    private void refundCapital(MemberStoreRechargeConsumeMapping mapping,MemberStoreRechargeRecord record, BigDecimal refundAmount,
                               MemberStoreRefundContext context) {
        // 增加本金余额
        record.setCapitalBalance(record.getCapitalBalance().add(refundAmount));
        mapping.setRefundCapitalAmount(mapping.getRefundCapitalAmount().add(refundAmount));
        mapping.setTotalRefundAmount(mapping.getTotalRefundAmount().add(refundAmount));

        // 添加到批量更新上下文
        context.getNeedUpdateRechargeRecords().add(record);
    }

    /**
     * 计算消费明细可退还的最大金额
     */
    private BigDecimal calculateMaxRefundableAmount(MemberStoreRechargeConsumeMapping mapping, boolean isGift) {
        // 原始消费金额
        BigDecimal originalAmount = isGift ?
                mapping.getConsumeGiftAmount() : mapping.getConsumeCapitalAmount();

        if (originalAmount == null || originalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 已退款金额
        BigDecimal refundedAmount = isGift ?
                mapping.getRefundGiftAmount() : mapping.getRefundCapitalAmount();

        refundedAmount = refundedAmount == null ? BigDecimal.ZERO : refundedAmount;

        // 可退还金额 = 原始金额 - 已退款金额
        return originalAmount.subtract(refundedAmount);
    }




}
