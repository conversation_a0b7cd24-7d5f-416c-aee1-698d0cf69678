package com.ly.titc.pms.member.asset.mediator.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberStoreUsageModeEnum;
import com.ly.titc.pms.member.asset.com.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.enums.StateEnum;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.dubbo.enums.MemberStoreUsageRuleModeEnum;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import com.ly.titc.pms.member.asset.mediator.rpc.dubbo.ecrm.MemberStoreUsageDecorator;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeRecordService;
import com.ly.titc.pms.member.asset.service.MemberStoreRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition 储值处理器
 * @since 2024-11-14 11:58
 */
@Slf4j
@Component
public class MemberStoreHandler {

    @Resource
    private MemberStoreRecordService memberStoreRecordService;
    @Resource
    private MemberStoreRechargeConsumeRecordService  consumeRecordService;
    @Resource
    private MemberStoreUsageDecorator memberStoreUsageDecorator;
    @Resource
    private MemberStoreConverter  converter;
    @Resource
    private MemberStoreExpireHandler expireHandler;


    /**
     * 计算总余额和可用余额
     * @return
     */
    public MemberMasterUsableBalanceDto calculateBalance(GetMemberUsableDto memberDto) {
        MemberMasterUsableBalanceDto storeBalanceDto = new MemberMasterUsableBalanceDto();
        //1.查询会员储值记录
        String memberNo = memberDto.getMemberNo();
        List<MemberStoreRechargeRecord> records = memberStoreRecordService.listByBalanceGtZero(memberNo);
        //2.计算总余额
        MemberBalanceDto balanceDto = calculateRecordBalance(records);
        storeBalanceDto.setTotalBalance(balanceDto.getTotalBalance());
        storeBalanceDto.setTotalCapitalBalance(balanceDto.getTotalCapitalBalance());
        storeBalanceDto.setTotalGiftBalance(balanceDto.getTotalGiftBalance());
        storeBalanceDto.setUsableRecords(converter.convertRe(records));
        //3.计算可用余额
        if(memberDto.getIsNeedUsage()){
            //3.1.查询该主体所有的使用规则
            List<MemberStoreUsageRuleAssetResp> ruleAssetResps =  memberStoreUsageDecorator.listRuleForAsset(memberDto.getMasterType(),memberDto.getMasterCode(),memberDto.getPlatformChannel());
            List<MemberStoreUsageRuleDto> usageRuleDtoList =  converter.convert(ruleAssetResps);
            //3.2.计算可用余额
            MemberMasterUsableBalanceDto usableBalanceInfoDto = calculateUsableSingleRule(records,usageRuleDtoList,memberDto);
            storeBalanceDto.setUsableTotalBalance(usableBalanceInfoDto.getUsableTotalBalance());
            storeBalanceDto.setUsableTotalCapitalBalance(usableBalanceInfoDto.getUsableTotalCapitalBalance());
            storeBalanceDto.setUsableTotalGiftBalance(usableBalanceInfoDto.getUsableTotalGiftBalance());
            storeBalanceDto.setUsableRecords(usableBalanceInfoDto.getUsableRecords());
        }
        return storeBalanceDto;
    }

    /**
     * 计算总消费金额
     * @param memberDto
     * @return
     */
    public MemberUsedAmountDto calculateUsedAmount(GetMemberUsableDto memberDto){
        List<String> consumeTypes = new ArrayList<>();
        consumeTypes.add(AssetConsumeTypeEnum.PAY.getCode());
        consumeTypes.add(AssetConsumeTypeEnum.REFUND.getCode());
        return consumeRecordService.getMasterTotalUsedAmount(memberDto.getMemberNo(),memberDto.getMasterType(),memberDto.getMasterCode(),consumeTypes);
    }




    /**
     * 计算总余额
     * @param records
     * @return
     */
    private MemberBalanceDto calculateRecordBalance(List<MemberStoreRechargeRecord> records) {
        BigDecimal totalBalance = BigDecimal.ZERO;
        BigDecimal totalCapitalBalance = BigDecimal.ZERO;
        BigDecimal totalGiftBalance = BigDecimal.ZERO;
        MemberBalanceDto balanceDto = new MemberBalanceDto()
                .setTotalBalance(totalBalance)
                .setTotalCapitalBalance(totalCapitalBalance)
                .setTotalGiftBalance(totalGiftBalance);
        if(CollectionUtils.isEmpty(records)){
            return balanceDto;
        }
        List<MemberStoreRechargeRecord> expiredGiftRecords = new ArrayList<>();

        for (MemberStoreRechargeRecord record : records) {
            totalBalance = totalBalance.add(record.getTotalBalance());
            totalCapitalBalance = totalCapitalBalance.add(record.getCapitalBalance());
            //判断是否过期
            if (StringUtils.isNotEmpty(record.getGiftExpireDate() )&& LocalDateUtil.parseByNormalDate(record.getGiftExpireDate()).isBefore(LocalDateUtil.now())) {
                if(record.getGiftBalance().compareTo(BigDecimal.ZERO) > 0){
                    expiredGiftRecords.add(record);
                }
                continue;
            }
            totalGiftBalance = totalGiftBalance.add(record.getGiftBalance());
        }
        //过期处理
        expireHandler.expireMemberStore(expiredGiftRecords);
        balanceDto.setTotalBalance(totalBalance);
        balanceDto.setTotalCapitalBalance(totalCapitalBalance);
        balanceDto.setTotalGiftBalance(totalGiftBalance);
        return balanceDto;
    }


    /**
     * 计算可用余额
     */
    private MemberMasterUsableBalanceDto calculateUsableSingleRule(List<MemberStoreRechargeRecord> records, List<MemberStoreUsageRuleDto> usageRuleDtoList, GetMemberUsableDto memberDto) {
        if(CollectionUtils.isEmpty(usageRuleDtoList)){
            log.warn("当前场景无法使用储值，请维护储值使用规则：{}", JSON.toJSONString(memberDto));
            throw new ServiceException(RespCodeEnum.CODE_1014);
        }
        //过滤使用模式为单一使用规则模式
        List<MemberStoreUsageRuleDto> singleRuleList = usageRuleDtoList.stream().filter(rule -> rule.getRuleMode().equals(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode())).collect(Collectors.toList());
        if(singleRuleList.size()>1){
            log.error("当前场景配置了多个使用规则，无法计算可用余额，请检查储值使用规则：{}", JSON.toJSONString(memberDto));
            throw new ServiceException(RespCodeEnum.CODE_1004);
        }
        List<MemberStoreUsageRuleDto> multipleRuleList = usageRuleDtoList.stream().filter(rule -> rule.getRuleMode().equals(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(singleRuleList) && CollectionUtils.isEmpty(multipleRuleList)){
            log.error("当前场景无法使用储值，请维护储值使用规则：{}", JSON.toJSONString(memberDto));
            throw new ServiceException(RespCodeEnum.CODE_1009);
        }
        MemberStoreUsageRuleDto singleRule ;
        if(CollectionUtils.isNotEmpty(singleRuleList)){
            singleRule = singleRuleList.get(0);
        } else {
            singleRule = null;
        }
        List<MemberStoreRechargeRecord> matchRecords = new ArrayList<>();
        List<MemberStoreRechargeRuleRecordDto> rechargeMatchRecords = new ArrayList<>();
        //匹配使用规则
        records.forEach(record -> {
            //单一模式需要根据使用方和平台适用范围匹配
            if(record.getUsageRuleMode().equals(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode())) {
                if(singleRule ==null){
                    log.error("当前场景无法使用储值，请维护储值使用规则：{}", JSON.toJSONString(memberDto));
                    return;
                }
                //不可用使用积分
                if(singleRule.getIsCanUse().equals(StateEnum.CLOSE.getState())){
                    log.warn("当前场景配置的不可使用积分：{}", JSON.toJSONString(memberDto));
                    return;
                }                //全部平台可用或者部分可用包含此平台
                if(!matchPlatformChannelScope(singleRule,record)){
                    return;
                }
                //判断储值使用模式
                MemberStoreRechargeRecord matchRecord = matchUsageMode(record, singleRule, memberDto);
                if(matchRecord != null){
                    matchRecords.add(matchRecord);
                    MemberStoreRechargeRuleRecordDto recordDto = converter.convertRecord(record);
                    //将规则ID赋值给记录 消费记录时需要使用
                    recordDto.setUsageRuleId(singleRule.getUsageRuleId());
                    recordDto.setUsageRuleDto(singleRule);
                    rechargeMatchRecords.add(recordDto);
                }
            }
            //混合规则模式
            if(record.getUsageRuleMode().equals(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode())){
                if(CollectionUtils.isEmpty(multipleRuleList)){
                    log.error("该记录为多模式的规则，无可用余额：{}", JSON.toJSONString(record));
                    return;
                }
                multipleRuleList.forEach(rule -> {
                    //判断规则Id是否匹配
                    if(!record.getUsageRuleId().equals(rule.getUsageRuleId())){
                        log.info("储值使用规则ID不匹配：memberNo:{},recordRuleId:{},规则：{}",record.getRechargeRecordNo(),record.getUsageRuleId(), JSON.toJSONString(rule));
                        return;
                    }
                    //不可用使用积分
                    if(rule.getIsCanUse().equals(StateEnum.CLOSE.getState())){
                        log.warn("当前场景配置的不可使用积分：{}", JSON.toJSONString(memberDto));
                        return;
                    }
                    //全部平台可用或者部分可用包含此平台
                    if(!matchPlatformChannelScope(rule,record)){
                        return;
                    }
                    //判断储值使用模式
                    MemberStoreRechargeRecord matchRecord = matchUsageMode(record, rule, memberDto);
                    if(matchRecord != null){
                        matchRecords.add(matchRecord);
                        MemberStoreRechargeRuleRecordDto recordDto = converter.convertRecord(record);
                        recordDto.setUsageRuleDto(rule);
                    }
                });
            }
        });

        MemberMasterUsableBalanceDto usableBalanceDto = new MemberMasterUsableBalanceDto();
        //计算可用可用额度
        MemberBalanceDto balanceDto=   calculateRecordBalance(matchRecords);
        usableBalanceDto.setUsableTotalBalance(balanceDto.getTotalBalance());
        usableBalanceDto.setUsableTotalCapitalBalance(balanceDto.getTotalCapitalBalance());
        usableBalanceDto.setUsableTotalGiftBalance(balanceDto.getTotalGiftBalance());
        usableBalanceDto.setUsableRecords(rechargeMatchRecords);
        return usableBalanceDto;
    }

    public boolean matchPlatformChannelScope(MemberStoreUsageRuleDto usageRuleDto, MemberStoreRechargeRecord record){
        List<String> platformScopeValues = usageRuleDto.getScopePlatformChannels();
        return platformScopeValues.contains(record.getPlatformChannel());
    }

    public MemberStoreRechargeRecord matchUsageMode(MemberStoreRechargeRecord record, MemberStoreUsageRuleDto usageRuleDto, GetMemberUsableDto memberDto){
        Integer usageMode = usageRuleDto.getUsageMode();
        MemberStoreUsageModeEnum usageModeEnum = MemberStoreUsageModeEnum.getByValue(usageMode);
        if(usageModeEnum == null){
            log.error("使用规则中的使用模式不存在：{}", JSON.toJSONString(usageRuleDto));
            throw new ServiceException(RespCodeEnum.CODE_1014);
        }
        Integer masterType = memberDto.getMasterType();
        String masterCode = memberDto.getMasterCode();
        switch (usageModeEnum){
            case ALONE:
                //仅充值门店可用
                if(record.getMasterType().equals(masterType) && record.getMasterCode().equals(masterCode)){
                    return record;
                }
                break;
            case ALL:
                //全部门店可用 todo 全部门店可用时需要区分集团和集团组的吗
                return record;
            case ALLOCATE:
                //指定门店可用
                List<MasterObject> masterObjects =  usageRuleDto.getUsageModeScopes();
                for (MasterObject masterObject : masterObjects) {
                    if(masterObject.getMasterType().equals(masterType) && masterObject.getMasterCode().equals(masterCode)){
                        return record;
                    }
                }
                break;
        }
        return null;
    }
}
