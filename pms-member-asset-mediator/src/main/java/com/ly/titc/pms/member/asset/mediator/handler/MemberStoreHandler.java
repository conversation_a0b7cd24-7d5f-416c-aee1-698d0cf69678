package com.ly.titc.pms.member.asset.mediator.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberStoreUsageModeEnum;
import com.ly.titc.pms.member.asset.com.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.com.enums.StateEnum;
import com.ly.titc.pms.member.asset.dal.entity.dto.MemberUsedAmountDto;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import com.ly.titc.pms.member.asset.dubbo.enums.MemberStoreUsageRuleModeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.StoreUseResultEnum;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.*;
import com.ly.titc.pms.member.asset.mediator.rpc.dubbo.ecrm.MemberStoreUsageDecorator;
import com.ly.titc.pms.member.asset.service.MemberStoreRechargeConsumeRecordService;
import com.ly.titc.pms.member.asset.service.MemberStoreRecordService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 储值处理器
 * @since 2024-11-14 11:58
 */
@Slf4j
@Component
public class MemberStoreHandler {

    @Resource
    private MemberStoreRecordService memberStoreRecordService;
    @Resource
    private MemberStoreRechargeConsumeRecordService consumeRecordService;
    @Resource
    private MemberStoreUsageDecorator memberStoreUsageDecorator;
    @Resource
    private MemberStoreConverter converter;
    @Resource
    private MemberStoreExpireHandler expireHandler;

    /**
     * 计算总余额和可用余额（上下文优化版）
     */
    public MemberStoreBalanceResult calculateBalance(GetMemberUsableDto memberDto) {
        // 校验参数
        validateArgs(memberDto);
        //组装上下文对象
        MemberStoreBalanceContext context = prepareContext(memberDto);
        try {
            //计算总余额
            calculateTotalBalance(context);
            if(memberDto.getIsNeedUsage() == null || !memberDto.getIsNeedUsage()){
               return buildResult(context);
            }
            //计算可用余额（可选）
            MemberStoreBalanceResult result =  preCheckUsageRules(context);
            if(!result.isSuccess()){
                return result;
            }
            //匹配规则
            matchUsageRules(context);
            //计算可用余额
            calculateUsableBalance(context);
            //异步处理过期记录
            processExpiredGiftRecordsAsync(context.getExpiredGiftRecords());
            //组装响应结果
           return buildResult(context);

        } catch (ServiceException e) {
            log.error("计算会员余额失败: {}", e.getMessage(), e);
            return new MemberStoreBalanceResult(StoreUseResultEnum.Fail,"计算会员余额失败");
        } catch (Exception e) {
            log.error("计算会员余额发生未知错误", e);
            return new MemberStoreBalanceResult(StoreUseResultEnum.Fail,"系统错误，请稍微重试");
        }
    }

    private MemberStoreBalanceResult preCheckUsageRules(MemberStoreBalanceContext context) {
        Map<String, List<MemberStoreUsageRuleDto>> multiRulesMap =  context.getMultiRulesMap();
        Map<String, List<MemberStoreUsageRuleDto>> singleRulesMap = context.getSingleRulesMap();

        List<MemberStoreUsageRuleDto> singleRules = singleRulesMap.get(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode());
        List<MemberStoreUsageRuleDto> multipleRules = multiRulesMap.get(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode());
        if (CollectionUtils.isEmpty(singleRules) && CollectionUtils.isEmpty(multipleRules)) {
            log.error("当前场景无法使用储值，请维护储值使用规则：{}", JSON.toJSONString(context.getMemberDto()));
            return new MemberStoreBalanceResult(StoreUseResultEnum.Rule_UnMatch,"当前场景无法使用储值，请维护储值使用规则");
        }
        // 规则数量校验
        if (singleRules.size() > 1) {
            log.error("当前场景配置了多个使用规则，无法计算可用余额，请检查储值使用规则：{}", JSON.toJSONString(context.getMemberDto()));
            return new MemberStoreBalanceResult(StoreUseResultEnum.Rule_UnMatch,"当前场景配置了多个使用规则，无法计算可用余额");
        }
        //使用场景

        return MemberStoreBalanceResult.builder().result(StoreUseResultEnum.Success).build();
    }

    //组装上下文对象
    private MemberStoreBalanceContext prepareContext(GetMemberUsableDto memberDto){
        String memberNo = memberDto.getMemberNo();
        Boolean isNeedUsage = memberDto.getIsNeedUsage();
        MemberStoreBalanceContext context = new MemberStoreBalanceContext(memberDto);
        //查询有余额的充值记录
        List<MemberStoreRechargeRecord> records = fetchRechargeRecords(memberNo);
        context.setRechargeRecords(records);
        if(isNeedUsage!=null && isNeedUsage) {
            //获取储值使用规则
            fetchUsageRules(context);
        }

        return context;

    }

    /**
     * 计算总消费金额
     */
    public MemberUsedAmountDto calculateUsedAmount(GetMemberUsableDto memberDto) {
        validateArgs(memberDto);

        List<String> consumeTypes = new ArrayList<>();
        consumeTypes.add(AssetConsumeTypeEnum.PAY.getCode());
        consumeTypes.add(AssetConsumeTypeEnum.REFUND.getCode());
        return consumeRecordService.getMasterTotalUsedAmount(memberDto.getMemberNo(),memberDto.getMasterType(),memberDto.getMasterCode(),consumeTypes);

    }

    /**
     * 验证会员信息
     */
    private void validateArgs(GetMemberUsableDto memberDto) {

        if (StringUtils.isEmpty(memberDto.getMemberNo())) {
            throw new ServiceException("会员编号不能为空",RespCodeEnum.CODE_400.getCode());

        }

        if (memberDto.getMasterType() == null || StringUtils.isEmpty(memberDto.getMasterCode())) {
            throw new ServiceException("业务主体类型和编码不能为空",RespCodeEnum.CODE_400.getCode());

        }
    }

    /**
     * 获取充值记录
     */
    private List<MemberStoreRechargeRecord> fetchRechargeRecords(String memberNo) {
        List<MemberStoreRechargeRecord> records = memberStoreRecordService.listByBalanceGtZero(memberNo);
        log.info("获取会员[{}]的储值记录，共{}条", memberNo, records.size());
        return records;
    }


    /**
     * 计算总余额
     */
    private void calculateTotalBalance(MemberStoreBalanceContext context) {
        List<MemberStoreRechargeRecord> records = context.getRechargeRecords();
        if (CollectionUtils.isEmpty(records)) {
            context.setUsableBalanceDto(initMasterBalanceDto());
            return;
        }

        BalanceCalculationResult result = calculateBalanceInternal(records);
        context.setExpiredGiftRecords(result.getExpiredGiftRecords());

        context.setTotalBalanceDto(result.getBalanceDto());
    }

    /**
     * 计算可用余额
     */
    private void calculateUsableBalance(MemberStoreBalanceContext context) {
        List<MemberStoreRechargeRecord> matchedRecords = context.getMatchedRecords();

        if (CollectionUtils.isEmpty(matchedRecords)) {
            context.setUsableBalanceDto(initMasterBalanceDto());
            return;
        }

        BalanceCalculationResult result = calculateBalanceInternal(matchedRecords);

        context.setUsableBalanceDto(result.getBalanceDto());
    }

    /**
     * 内部余额计算逻辑（提取的公共方法）
     */
    private BalanceCalculationResult calculateBalanceInternal(List<MemberStoreRechargeRecord> records) {
        BigDecimal totalBalance = BigDecimal.ZERO;
        BigDecimal totalCapitalBalance = BigDecimal.ZERO;
        BigDecimal totalGiftBalance = BigDecimal.ZERO;

        // 过滤过期礼品记录并计算总余额
        List<MemberStoreRechargeRecord> expiredGiftRecords = new ArrayList<>();

        for (MemberStoreRechargeRecord record : records) {
            totalBalance = totalBalance.add(record.getTotalBalance());
            totalCapitalBalance = totalCapitalBalance.add(record.getCapitalBalance());

            if (isGiftBalanceExpired(record)) {
                expiredGiftRecords.add(record);
            } else {
                totalGiftBalance = totalGiftBalance.add(record.getGiftBalance());
            }
        }

        // 构建结果对象
        MemberBalanceDto balanceDto = new MemberBalanceDto()
                .setTotalBalance(totalBalance)
                .setTotalCapitalBalance(totalCapitalBalance)
                .setTotalGiftBalance(totalGiftBalance);

        return new BalanceCalculationResult(balanceDto, expiredGiftRecords);
    }

    /**
     * 余额计算结果封装类
     */
    @Getter
    private static class BalanceCalculationResult {
        private final MemberBalanceDto balanceDto;
        private final List<MemberStoreRechargeRecord> expiredGiftRecords;

        public BalanceCalculationResult(MemberBalanceDto balanceDto,
                                        List<MemberStoreRechargeRecord> expiredGiftRecords) {
            this.balanceDto = balanceDto;
            this.expiredGiftRecords = expiredGiftRecords;
        }

    }

    private MemberBalanceDto initMasterBalanceDto() {

        return new MemberBalanceDto()
                .setTotalBalance(BigDecimal.ZERO)
                .setTotalCapitalBalance(BigDecimal.ZERO)
                .setTotalGiftBalance(BigDecimal.ZERO);
    }



    /**
     * 获取使用规则
     */
    private void  fetchUsageRules(MemberStoreBalanceContext context) {
        GetMemberUsableDto memberDto = context.getMemberDto();
         //获取储值使用规则
        List<MemberStoreUsageRuleAssetResp> ruleAssetResp = memberStoreUsageDecorator.listRuleForAsset(memberDto.getMasterType(),
                                                                        memberDto.getMasterCode(), memberDto.getPlatformChannel());
        List<MemberStoreUsageRuleDto> rules = Optional.ofNullable(converter.convert(ruleAssetResp)).orElse(Collections.emptyList());
        // 分组规则（添加空值过滤，防止 getRuleMode() 返回 null）
        Map<String, List<MemberStoreUsageRuleDto>> ruleModeMap = rules.stream().filter(rule -> rule.getRuleMode() != null)
                .collect(Collectors.groupingBy(MemberStoreUsageRuleDto::getRuleMode));
        // 获取分组结果
        List<MemberStoreUsageRuleDto> singleRules = ruleModeMap.getOrDefault(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode(), Collections.emptyList());

        List<MemberStoreUsageRuleDto> multipleRules = ruleModeMap.getOrDefault(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode(), Collections.emptyList());
        // 将规则存入上下文
        context.getSingleRulesMap().put(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode(), singleRules);
        context.getMultiRulesMap().put(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode(), multipleRules);

    }

    /**
     * 匹配使用规则
     */
    private void matchUsageRules(MemberStoreBalanceContext context) {
        List<MemberStoreRechargeRecord> records = context.getRechargeRecords();
        GetMemberUsableDto memberDto = context.getMemberDto();
        // 获取规则
        List<MemberStoreUsageRuleDto> singleRules = context.getSingleRulesMap().getOrDefault(MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode(), Collections.emptyList());

        List<MemberStoreUsageRuleDto> multipleRules = context.getMultiRulesMap().getOrDefault(MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode(), Collections.emptyList());

        for (MemberStoreRechargeRecord record : records) {
            String ruleMode = record.getUsageRuleMode();

            if (MemberStoreUsageRuleModeEnum.SINGLE_STORE.getMode().equals(ruleMode)) {
                processSingleModeRule(record, singleRules, memberDto, context);
            } else if (MemberStoreUsageRuleModeEnum.MULTI_STORE.getMode().equals(ruleMode)) {
                processMultiModeRule(record, multipleRules, memberDto, context);
            }
        }
    }

    /**
     * 处理单一模式规则匹配
     */
    private void processSingleModeRule(MemberStoreRechargeRecord record, List<MemberStoreUsageRuleDto> singleRules, GetMemberUsableDto memberDto,
            MemberStoreBalanceContext context) {

        if (CollectionUtils.isEmpty(singleRules)) {
            log.error("单一模式规则不存在，无法匹配：{}", record.getRechargeRecordNo());
            return;
        }

        MemberStoreUsageRuleDto rule = singleRules.get(0);

        // 规则可用性校验
        if (StateEnum.CLOSE.getState().equals(rule.getIsCanUse())) {
            log.warn("规则不可用，跳过匹配：{}", rule.getUsageRuleId());
            return;
        }

        // 平台渠道匹配
        if (!matchPlatformChannelScope(rule, record)) {
            return;
        }
        //使用场景匹配
        if (!matchUsageSceneScope(rule, record)) {
            return;
        }

        // 使用模式匹配
        MemberStoreRechargeRecord matchedRecord = matchUsageMode(record, rule, memberDto);
        if (matchedRecord != null) {
            context.addMatchedRecord(matchedRecord);

            MemberStoreRechargeRuleRecordDto ruleRecord = converter.convertRecord(record);
            ruleRecord.setUsageRuleId(rule.getUsageRuleId());
            ruleRecord.setUsageRuleDto(rule);
            context.addMatchedRuleRecord(ruleRecord);
        }
    }

    private boolean matchUsageSceneScope(MemberStoreUsageRuleDto rule, MemberStoreRechargeRecord record) {
        //todo 匹配使用场景
        return true;
    }

    /**
     * 处理多模式规则匹配
     */
    private void processMultiModeRule(MemberStoreRechargeRecord record, List<MemberStoreUsageRuleDto> multipleRules, GetMemberUsableDto memberDto,
                                      MemberStoreBalanceContext context) {

        if (CollectionUtils.isEmpty(multipleRules)) {
            log.error("多模式规则不存在，无法匹配：{}", record.getRechargeRecordNo());
            return;
        }

        for (MemberStoreUsageRuleDto rule : multipleRules) {
            // 规则ID匹配
            if (!record.getUsageRuleId().equals(rule.getUsageRuleId())) {
                log.warn("规则ID不匹配，跳过匹配：{}", rule.getUsageRuleId());
                continue;
            }

            // 规则可用性校验
            if (StateEnum.CLOSE.getState().equals(rule.getIsCanUse())) {
                log.warn("规则不可用，跳过匹配：{}", rule.getUsageRuleId());
                continue;
            }

            // 平台渠道匹配
            if (!matchPlatformChannelScope(rule, record)) {
                continue;
            }

            // 使用模式匹配
            MemberStoreRechargeRecord matchedRecord = matchUsageMode(record, rule, memberDto);
            if (matchedRecord != null) {
                context.addMatchedRecord(matchedRecord);
                MemberStoreRechargeRuleRecordDto ruleRecord = converter.convertRecord(record);
                ruleRecord.setUsageRuleDto(rule);
                context.addMatchedRuleRecord(ruleRecord);
            }
        }
    }



    /**
     * 构建结果对象
     */
    private MemberStoreBalanceResult buildResult(MemberStoreBalanceContext context) {
        MemberMasterUsableBalanceDto usableBalanceDto = new MemberMasterUsableBalanceDto();

        // 设置总余额
        MemberBalanceDto totalBalance = context.getTotalBalanceDto();
        usableBalanceDto.setTotalBalance(totalBalance.getTotalBalance());
        usableBalanceDto.setTotalCapitalBalance(totalBalance.getTotalCapitalBalance());
        usableBalanceDto.setTotalGiftBalance(totalBalance.getTotalGiftBalance());

        // 设置可用余额
        if (context.getMemberDto().getIsNeedUsage()) {
            MemberBalanceDto usableBalance = context.getUsableBalanceDto();
            usableBalanceDto.setUsableTotalBalance(usableBalance.getTotalBalance());
            usableBalanceDto.setUsableTotalCapitalBalance(usableBalance.getTotalCapitalBalance());
            usableBalanceDto.setUsableTotalGiftBalance(usableBalance.getTotalGiftBalance());
            // 设置匹配的记录
            usableBalanceDto.setUsableRecords(context.getMatchedRuleRecords());
        } else {
            // 如果不需要使用规则，可用余额等于总余额
            usableBalanceDto.setUsableTotalBalance(totalBalance.getTotalBalance());
            usableBalanceDto.setUsableTotalCapitalBalance(totalBalance.getTotalCapitalBalance());
            usableBalanceDto.setUsableTotalGiftBalance(totalBalance.getTotalGiftBalance());
        }
        return new MemberStoreBalanceResult(StoreUseResultEnum.Success,"",usableBalanceDto);
    }

    /**
     * 平台渠道范围匹配
     */
    private boolean matchPlatformChannelScope(MemberStoreUsageRuleDto usageRuleDto, MemberStoreRechargeRecord record) {
        if (CollectionUtils.isEmpty(usageRuleDto.getScopePlatformChannels())) {
            log.warn("使用规则未设置平台渠道范围，无法匹配：{}", usageRuleDto.getUsageRuleId());
            return false;
        }
        return usageRuleDto.getScopePlatformChannels().contains(record.getPlatformChannel());
    }

    /**
     * 使用模式匹配
     */
    public MemberStoreRechargeRecord matchUsageMode(MemberStoreRechargeRecord record,
                                                    MemberStoreUsageRuleDto usageRuleDto,
                                                    GetMemberUsableDto memberDto) {
        Integer usageMode = usageRuleDto.getUsageMode();
        MemberStoreUsageModeEnum usageModeEnum = MemberStoreUsageModeEnum.getByValue(usageMode);

        if (usageModeEnum == null) {
            log.error("使用规则中的使用模式不存在：{}", JSON.toJSONString(usageRuleDto));
            throw new ServiceException(RespCodeEnum.Store_unSetting_Rule);
        }

        Integer masterType = memberDto.getMasterType();
        String masterCode = memberDto.getMasterCode();

        switch (usageModeEnum) {
            case ALONE:
                return record.getMasterType().equals(masterType) && record.getMasterCode().equals(masterCode)
                        ? record : null;
            case ALL:
                return record;
            case ALLOCATE:
                return usageRuleDto.getUsageModeScopes().stream().anyMatch(scope -> scope.getMasterType().equals(masterType) &&
                                scope.getMasterCode().equals(masterCode))
                        ? record : null;
            default:
                log.warn("未知使用模式：{}", usageMode);
                return null;
        }
    }

    /**
     * 判断礼品余额是否过期
     */
    private boolean isGiftBalanceExpired(MemberStoreRechargeRecord record) {
        if (StringUtils.isEmpty(record.getGiftExpireDate())) {
            return false;
        }
        return LocalDateUtil.parseByNormalDate(record.getGiftExpireDate())
                .isBefore(LocalDateUtil.now()) &&
                record.getGiftBalance().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 异步处理过期礼品记录
     */
    private void processExpiredGiftRecordsAsync(List<MemberStoreRechargeRecord> expiredGiftRecords) {
        if (CollectionUtils.isEmpty(expiredGiftRecords)) {
            return;
        }
        // 实际项目中可使用线程池或异步服务
        expireHandler.expireMemberStore(expiredGiftRecords);
    }
}