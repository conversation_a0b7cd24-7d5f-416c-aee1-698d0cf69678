package com.ly.titc.pms.member.asset.mediator.entity;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 15:25
 */
@Data
@Accessors(chain = true)
public class BaseMemberDto {

        /**
        * 请求追踪id
        */
        private String trackingId ;

        /**
         * 会员号
         */
        private String memberNo;

        /**
         * 操作人
         */
        private String operator;
        /**
         * 积分主体类型   1:集团 2:门店 3:酒馆
         * 本条记录操作的主体
         */
        private Integer masterType;

        /**
         * 积分主体CODE ELONG 集团编码 门店编码
         */
        private String masterCode;

        /**
         * 酒馆组code ELONG (冗余)
         */
        private String clubCode;

        /**
         * 集团code （冗余）
         */
        private String blocCode;

        /**
         * 酒店code （冗余）
         */
        private String hotelCode;

        /**
         * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
         */
        private String platformChannel;
}
