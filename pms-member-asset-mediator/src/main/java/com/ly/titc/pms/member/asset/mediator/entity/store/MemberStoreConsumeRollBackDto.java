package com.ly.titc.pms.member.asset.mediator.entity.store;

import com.ly.titc.pms.member.asset.mediator.entity.BaseMemberDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberStoreConsumeRollBackDto extends BaseMemberDto {

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 退款业务申请单号
     */

    private String bizRefundConsumeNo;

    /**
     * 来源系统：会员-MEMBER，商品部-SHOP 餐饮-FOOD 微订房-WEBOOKNG 收银台——CASHIER
     */

    private String sourceSystem;

    /**
     * 业务原消费单号  （同会员原消费单号二选一）
     */
    private String bizConsumeNo;

    /**
     * 会员原消费单号
     */
    private String consumeNo;

    /**
     * 备注
     */
    private String remark;





}
