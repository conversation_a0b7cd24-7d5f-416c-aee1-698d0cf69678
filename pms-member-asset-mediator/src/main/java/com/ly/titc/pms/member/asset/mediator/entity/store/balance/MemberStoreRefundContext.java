package com.ly.titc.pms.member.asset.mediator.entity.store.balance;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeConsumeMapping;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberStoreRechargeRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberStoreRefundContext {

    /**
     * 消费记录
     */

    private List<MemberStoreRechargeConsumeMapping> originalMappings;

    /**
     * 充值记录map，key为充值单号
     */
   private Map<String,MemberStoreRechargeRecord> rechargeRecordMap;

    /**
     * 需要更新的充值记录
     */
   private List<MemberStoreRechargeRecord> needUpdateRechargeRecords;

    /**
     * 需要更新的消费记录mapping
     */
    private List<MemberStoreRechargeConsumeMapping> needUpdateConsumeMappings;
}
