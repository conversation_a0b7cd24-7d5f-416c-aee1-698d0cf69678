package com.ly.titc.pms.member.asset.mediator.config;


import com.ly.titc.common.factory.YmlConfigFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;


/**
 * 中间件配置
 *
 * @classname MediatorConfig
 * @descrition 中间件配置
 * <AUTHOR>
 * @since 2023/8/3 17:35
 */
@Slf4j
@Data
@Configuration
@PropertySource(value = {"classpath:mediator.yml"}, factory = YmlConfigFactory.class)
public class MediatorConfig {

}
