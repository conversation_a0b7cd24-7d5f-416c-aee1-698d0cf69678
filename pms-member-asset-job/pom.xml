<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>pms-member-asset-parent</artifactId>
    <groupId>com.ly.titc</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>pms-member-asset-job</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-asset-interfaces</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-asset-mediator</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.lianjiatech</groupId>
      <artifactId>retrofit-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.turbomq</groupId>
      <artifactId>turbomq-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.tcbase</groupId>
      <artifactId>cache</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>springboot-start-mq</artifactId>
    </dependency>
    <!-- mapStruct-lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <finalName>titc.java.in.stay.service.job</finalName>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>test/*</exclude>
          <exclude>qa/*</exclude>
          <exclude>stage/*</exclude>
          <exclude>product/*</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources/${package.environment}</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.0.RELEASE</version>
        <configuration>
          <mainClass>com.ly.titc.pms.member.asset.job.JobApplication</mainClass>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <showWarnings>true</showWarnings>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <!-- 引入lombok-->
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <!-- 引入mapstruct-processor-->
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${lombok-mapstruct-binding.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>