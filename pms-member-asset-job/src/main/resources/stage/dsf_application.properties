##服务组信息
dsf.service.config.name=titc.java.in.stay.service.job
# dsf.service.config.registryhost=qa.dsf2.17usoft.com
# 由adaptor域名需要调整为控制中心域名；本地调试不需要注册到DSF上时，不配置该参数则不会进行注册流程
dsf.service.config.registryhost=controlcenter.dsfv3.t.17usoft.com

##dsf环境
dsf.service.config.env=stage

# 日志框架相关配置，若不配置使用默认值。详情见后文日志配置文档
dsf.logging.path=/data/logs/skynet-titc.java.in.stay.service.job/dsflog
dsf.logging.level=DEBUG