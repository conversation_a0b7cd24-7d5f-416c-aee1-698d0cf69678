package com.ly.titc.pms.member.asset.job.handler.tag;

import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @classname DemoTagHandler
 * @descrition demo
 * @since 2024/1/10 16:10
 */
@Slf4j
@Component
public class DemoTagHand<PERSON> extends AbstractTurboMQTagHandler {

  @Override
  public boolean execute(String messageId, String msg) {
    return false;
  }

  @Override
  public String getTag() {
    return null;
  }
}
