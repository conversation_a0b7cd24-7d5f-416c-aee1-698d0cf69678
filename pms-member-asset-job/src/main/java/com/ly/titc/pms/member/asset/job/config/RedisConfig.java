package com.ly.titc.pms.member.asset.job.config;

import com.ly.tcbase.cacheclient.CacheClientHA;
import com.ly.titc.common.factory.RedisFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 * @classname RedisConfig
 * @descrition
 * @since 2019/11/26 14:45
 */
@Configuration
@PropertySource("classpath:tcbase.properties")
public class RedisConfig {

	@Value("${redis.name}")
	private String redisName;

	@Bean(name = "redisFactory")
	public RedisFactory getRedisClient(@Qualifier("cacheClient") CacheClientHA cacheClient){
		RedisFactory rf = new RedisFactory();
		rf.setCacheClient(cacheClient);
		return rf;
	}
	
	@Bean(name = "cacheClient")
	public CacheClientHA getCacheClientHA(){
		CacheClientHA cacheClient = new CacheClientHA(redisName, true);
		return cacheClient;
	}
}
