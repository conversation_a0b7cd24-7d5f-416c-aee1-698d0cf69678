package com.ly.titc.pms.member.asset.job.handler.topic;

import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTopicHandler;
import com.ly.titc.springboot.mq.manager.TurboMQTagHandlerManager;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @classname DemoTagHandler
 * @descrition demo
 * @since 2024/1/10 16:10
 */
@Slf4j
@Component
public class DemoTopicHandler extends AbstractTurboMQTopicHandler {

  @Override
  public boolean execute(String tag, String messageId, String msg) {

    AbstractTurboMQTagHandler handler = TurboMQTagHandlerManager.getInstance(tag);
    if (Objects.isNull(handler)) {
      log.warn("this tag has not handler to process!!tag:{};messageId:{}", tag, messageId);
      return true;
    }
    return handler.execute(messageId, msg);
  }

  @Override
  public String getTopic() {
    return null;
  }
}
