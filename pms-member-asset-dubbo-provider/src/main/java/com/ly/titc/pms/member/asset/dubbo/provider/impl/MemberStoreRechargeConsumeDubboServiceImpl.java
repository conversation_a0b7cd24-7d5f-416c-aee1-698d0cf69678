package com.ly.titc.pms.member.asset.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.ListMemberStoreConsumeByBusinessNoReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.ListRechargeConsumeRecordMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.PageMemberStoreConsumeMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreRechargeConsumeDubboService;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberStoreConsumeRecordDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberTradeConsumeRecordDto;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoreRechargeConsumeMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-19 14:01
 */
@Slf4j
@Validated
@DubboService
public class MemberStoreRechargeConsumeDubboServiceImpl implements MemberStoreRechargeConsumeDubboService {
    @Resource
    private MemberStoreRechargeConsumeMedService consumeMedService;
    @Resource
    private MemberStoreConverter converter;


    @Override
    public Response<MemberStoreConsumeRecordResp> listByBusinessNo(@Valid ListMemberStoreConsumeByBusinessNoReq req) {
        return null;
    }

    @Override
    public Response<Pageable<MemberStoreConsumeRecordResp>> page(@Valid PageMemberStoreConsumeMemberReq req) {

        Pageable<MemberStoreConsumeRecordDto> page = consumeMedService.pageConsumeRecord(converter.convert(req));
        List<MemberStoreConsumeRecordDto> list = page.getDatas();
        if (CollectionUtils.isEmpty(list)) {
            return Response.success(Pageable.empty());
        }
        return Response.success(PageableUtil.convert(page, converter.convertMemberStoreConsumeRecordRespList(list)));
    }

    @Override
    public Response<List<MemberTradeConsumeRecordResp>> listRechargeConsumeRecord(ListRechargeConsumeRecordMemberReq req) {
        List<MemberTradeConsumeRecordDto> list = consumeMedService.listRechargeConsumeRecord(req.getMemberNo(), req.getTradeNoList());
        return Response.success(converter.convertMemberTradeConsumeRecordRespList(list));
    }
}
