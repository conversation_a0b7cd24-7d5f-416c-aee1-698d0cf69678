package com.ly.titc.pms.member.asset.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTotalAmountResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreDubboService;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberStoreAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.balance.MemberTotalAmountDto;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoredMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-9 14:15
 */
@Slf4j
@Validated
@DubboService
public class MemberStoreDubboServiceImpl implements MemberStoreDubboService {
    @Resource
    private MemberStoredMedService storedMedService;
    @Resource
    private MemberStoreConverter converter;

    @Override
    public Response<MemberTotalAmountResp> getTotalAccountAmount(@Valid BaseMemberReq req) {
        MemberTotalAmountDto dto= storedMedService.getTotalAccountAmount(converter.convert(req));
        return Response.success(converter.convert(dto));
    }

    @Override
    public Response<MemberStoreAccountResp> getUsableMasterAccount(@Valid GetMemberUsableReq req) {
        GetMemberUsableDto reqDto= converter.convert(req);
        reqDto.setIsNeedUsage(true);
        MemberStoreAccountDto dto= storedMedService.getUsableMasterAccount(reqDto);
        return Response.success(converter.convert(dto));
    }

}
