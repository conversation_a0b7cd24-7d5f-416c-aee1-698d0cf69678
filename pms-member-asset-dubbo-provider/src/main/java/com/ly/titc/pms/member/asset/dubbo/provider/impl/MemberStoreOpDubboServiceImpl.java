package com.ly.titc.pms.member.asset.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeCalResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRollBackResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.enums.RechargeTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreOpDubboService;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.store.*;
import com.ly.titc.pms.member.asset.mediator.service.MemberStoredOPMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:49
 */
@Slf4j
@Validated
@DubboService
public class MemberStoreOpDubboServiceImpl implements MemberStoreOpDubboService {
    @Resource
    private MemberStoreConverter converter;
    @Resource
    private MemberStoredOPMedService storeOPMedService;


    @Override
    public Response<MemberStoreRecordOPResultResp> recharge(@Valid MemberStoreRechargeReq req) {
        MemberStoreRechargeDto rechargeDto= converter.convert(req);
        rechargeDto.setTradeType(RechargeTypeEnum.RECHARGE.getType());
        MemberStoreRecordOPResultDto dto = storeOPMedService.recharge(rechargeDto);
        return Response.success(converter.convert(dto));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> rechargeRollback(MemberStoreRechargeRollBackReq req) {
        MemberStoreRechargeRollBackDto rollbackDto = converter.convert(req);
        rollbackDto.setTradeType(RechargeTypeEnum.RECHARGE_REFUND.getType());
        MemberStoreRecordOPResultDto dto = storeOPMedService.rechargeRollback(rollbackDto);
        return Response.success(converter.convert(dto));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> consumeStore(@Valid MemberStoreConsumeReq req) {
        MemberStoreRecordOPResultDto dto = storeOPMedService.consumeStore(converter.convert(req));
        return Response.success(converter.convert(dto));
    }

    @Override
    public Response<MemberStoreConsumeCalResp> consumeStoreCal(MemberStoreConsumePreCalReq req) {
        MemberStoreConsumeCalDto consumeCalDto = storeOPMedService.consumeStoreCal(converter.convert(req));
        MemberStoreConsumeCalResp resp = converter.convert(consumeCalDto);
        return Response.success(resp);
    }

    @Override
    public Response<MemberStoreConsumeRollBackResp> consumeRollback(MemberStoreConsumeRollBackReq req) {
        MemberStoreConsumeRollBackResultDto dto = storeOPMedService.consumeRollback(converter.convert(req));
        MemberStoreConsumeRollBackResp resp = converter.convertConsumeRollBack(dto);
        return Response.success(resp);
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> freeze(MemberStoreFreezeReq req) {
        MemberStoreRecordOPResultDto dto =  storeOPMedService.freeze(converter.convert(req));
        return Response.success(converter.convert(dto));
    }

    @Override
    public Response<MemberStoreRecordOPResultResp> unFreeze(UnfreezeConsumeRecordNoReq req) {
        MemberStoreRecordOPResultDto dto=  storeOPMedService.unFreeze(converter.convert(req));
        return Response.success(converter.convert(dto));
    }
}
