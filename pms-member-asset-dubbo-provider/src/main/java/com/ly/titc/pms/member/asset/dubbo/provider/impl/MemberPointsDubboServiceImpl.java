package com.ly.titc.pms.member.asset.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.asset.biz.MemberPointAccountBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BatchMemberPeriodReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BatchMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointsDubboService;
import com.ly.titc.pms.member.asset.dubbo.provider.converter.MemberPointConverter;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberPointAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.PageMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import com.ly.titc.pms.member.asset.mediator.service.MemberPointRecordMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员积分Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2024/11/7 20:15
 */
@Slf4j
@Validated
@DubboService
public class MemberPointsDubboServiceImpl implements MemberPointsDubboService {

    @Resource
    private MemberPointRecordMedService memberPointRecordMedService;
    @Resource
    private MemberPointAccountBiz pointAccountBiz;

    @Resource
    private MemberPointConverter memberPointConverter;

    @Resource
    private MemberStoreConverter converter;


    @Override
    public Response<MemberTotalPointResp> getTotalAccountPoints(BaseMemberReq req) {
        MemberPointAccountInfo accountInfo =  pointAccountBiz.getByMemberNo(req.getMemberNo());
        MemberTotalPointResp resp=  memberPointConverter.convert(accountInfo);
        if(resp == null){
            return Response.success(new MemberTotalPointResp());
        }
        return Response.success(resp);
    }

    @Override
    public Response<List<MemberTotalPointResp>> getTotalAccountPointsList(BatchMemberReq req) {
        List<MemberPointAccountInfo> accountInfoList =  pointAccountBiz.getByMemberNoList(req.getMemberNoList());
        return Response.success(memberPointConverter.convertMemberList(accountInfoList));
    }

    @Override
    public Response<MemberPointPeriodResp> getTotalAccountPointsPeriodList(BatchMemberPeriodReq req) {
        List<MemberPointAccountInfo> memberPointAccountInfoList = pointAccountBiz.getByMemberNoListAndPeriod(req.getMemberNoList(), req.getSdate(), req.getEdate());
        // 分组计算每个会员的积分
        memberPointAccountInfoList
                .stream()
                .groupBy

        return null;
    }

    @Override
    public Response<MemberPointAccountResp> getUsableMasterAccount(GetMemberUsableReq req) {
        GetMemberUsableDto dto= converter.convert(req);
        dto.setIsNeedUsage(true);
        MemberPointAccountDto  accountDto = memberPointRecordMedService.getUsableMasterAccount(dto);
        return Response.success(memberPointConverter.convert(accountDto));
    }



    @Override
    public Response<Pageable<MemberPointsFlowInfoResp>> pageMemberPointsFlow(PageMemberPointsFlowMemberReq request) {
        PageMemberPointDto pageDto = converter.convertPageDto(request);
        Pageable<MemberPointDto> pageable = memberPointRecordMedService.pageMemberPoint(pageDto);
        List<MemberPointDto> dtoList = pageable.getDatas();
        if (CollectionUtils.isEmpty(dtoList)) {
            return Response.success(Pageable.empty());
        }
        return Response.success(PageableUtil.convert(pageable, memberPointConverter.convertMemberPointsFlowInfoRespList(dtoList)));
    }
}
