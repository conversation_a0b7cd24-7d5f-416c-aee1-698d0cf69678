package com.ly.titc.pms.member.asset.dubbo.provider.impl;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.asset.biz.MemberPointAccountBiz;
import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BatchMemberPeriodReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BatchMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.GetMemberUsableReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointPeriodResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointsDubboService;
import com.ly.titc.pms.member.asset.dubbo.provider.converter.MemberPointConverter;
import com.ly.titc.pms.member.asset.mediator.convert.MemberStoreConverter;
import com.ly.titc.pms.member.asset.mediator.entity.GetMemberUsableDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberPointAccountDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.PageMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import com.ly.titc.pms.member.asset.mediator.service.MemberPointRecordMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员积分Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2024/11/7 20:15
 */
@Slf4j
@Validated
@DubboService
public class MemberPointsDubboServiceImpl implements MemberPointsDubboService {

    @Resource
    private MemberPointRecordMedService memberPointRecordMedService;
    @Resource
    private MemberPointAccountBiz pointAccountBiz;

    @Resource
    private MemberPointConverter memberPointConverter;

    @Resource
    private MemberStoreConverter converter;


    @Override
    public Response<MemberTotalPointResp> getTotalAccountPoints(BaseMemberReq req) {
        MemberPointAccountInfo accountInfo =  pointAccountBiz.getByMemberNo(req.getMemberNo());
        MemberTotalPointResp resp=  memberPointConverter.convert(accountInfo);
        if(resp == null){
            return Response.success(new MemberTotalPointResp());
        }
        return Response.success(resp);
    }

    @Override
    public Response<List<MemberTotalPointResp>> getTotalAccountPointsList(BatchMemberReq req) {
        List<MemberPointAccountInfo> accountInfoList =  pointAccountBiz.getByMemberNoList(req.getMemberNoList());
        return Response.success(memberPointConverter.convertMemberList(accountInfoList));
    }

    @Override
    public Response<MemberPointPeriodResp> getTotalAccountPointsPeriodList(BatchMemberPeriodReq req) {
        List<MemberPointAccountInfo> memberPointAccountInfoList = pointAccountBiz.getByMemberNoListAndPeriod(req.getMemberNoList(), req.getSdate(), req.getEdate());

        MemberPointPeriodResp resp = new MemberPointPeriodResp();

        // 如果没有查询到数据，返回空列表
        if (CollectionUtils.isEmpty(memberPointAccountInfoList)) {
            resp.setMemberPointPeriodList(new ArrayList<>());
            return Response.success(resp);
        }

        // 分组计算每个会员的积分
        Map<String, List<MemberPointAccountInfo>> memberGroupMap = memberPointAccountInfoList
                .stream()
                .collect(Collectors.groupingBy(MemberPointAccountInfo::getMemberNo));

        // 构建响应结果
        List<MemberPointPeriodResp.MemberPointPeriodDto> memberPointPeriodList = memberGroupMap.entrySet()
                .stream()
                .map(entry -> {
                    String memberNo = entry.getKey();
                    List<MemberPointAccountInfo> accountInfos = entry.getValue();

                    // 计算该会员在指定时间段内的总积分
                    int totalScore = accountInfos.stream()
                            .mapToInt(info -> info.getTotalScore() != null ? info.getTotalScore() : 0)
                            .sum();

                    MemberPointPeriodResp.MemberPointPeriodDto dto = new MemberPointPeriodResp.MemberPointPeriodDto();
                    dto.setMemberNo(memberNo);
                    dto.setTotalScore(totalScore);
                    return dto;
                })
                .collect(Collectors.toList());

        resp.setMemberPointPeriodList(memberPointPeriodList);

        return Response.success(resp);
    }

    @Override
    public Response<MemberPointAccountResp> getUsableMasterAccount(GetMemberUsableReq req) {
        GetMemberUsableDto dto= converter.convert(req);
        dto.setIsNeedUsage(true);
        MemberPointAccountDto  accountDto = memberPointRecordMedService.getUsableMasterAccount(dto);
        return Response.success(memberPointConverter.convert(accountDto));
    }



    @Override
    public Response<Pageable<MemberPointsFlowInfoResp>> pageMemberPointsFlow(PageMemberPointsFlowMemberReq request) {
        PageMemberPointDto pageDto = converter.convertPageDto(request);
        Pageable<MemberPointDto> pageable = memberPointRecordMedService.pageMemberPoint(pageDto);
        List<MemberPointDto> dtoList = pageable.getDatas();
        if (CollectionUtils.isEmpty(dtoList)) {
            return Response.success(Pageable.empty());
        }
        return Response.success(PageableUtil.convert(pageable, memberPointConverter.convertMemberPointsFlowInfoRespList(dtoList)));
    }
}
