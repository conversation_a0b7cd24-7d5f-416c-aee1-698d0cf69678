package com.ly.titc.pms.member.asset.dubbo.provider;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @ClassName: DubboProviderApplication
 * @Description: 启动类
 * @date 2019年5月13日
 */
@ComponentScan(basePackages = {"com.ly.titc.pms.member.asset", "com.ly.titc.springboot"})
@RetrofitScan("com.ly.titc.pms.member.asset.facade")
@SpringBootApplication
public class DubboProviderApplication {

	/**
	 * run
	 *
	 * @param args
	 */
	public static void main(String[] args) {
		SpringApplication.run(DubboProviderApplication.class, args);
	}
}
