package com.ly.titc.pms.member.asset.dubbo.provider.converter;

import com.ly.titc.pms.member.asset.dal.entity.po.MemberPointAccountInfo;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointAccountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberPointRecordResultResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.mediator.entity.point.*;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberPointConverter
 * @Date：2024-12-9 11:54
 * @Filename：MemberPointConverter
 */
@Mapper(componentModel = "spring")
public interface MemberPointConverter {

    List<MemberPointsFlowInfoResp> convertMemberPointsFlowInfoRespList(List<MemberPointDto> dtoList);

    MemberPointsFlowInfoResp convertMemberPointsFlowInfoResp(MemberPointDto dto);

    MemberTotalPointResp convert(MemberPointAccountInfo accountInfo);

    List<MemberTotalPointResp> convertMemberList(List<MemberPointAccountInfo> accountInfo);

    @Mappings({
            @Mapping(target = "totalPointResp", source = "totalPointDto"),
            @Mapping(target = "masterPointResp", source = "masterPointDto")
    })
    MemberPointAccountResp convert(MemberPointAccountDto accountDto);

    ReceiveMemberPointDto convert(ReceiveMemberPointReq req);

    MemberPointRecordResultResp convert(SaveMemberPointRecordResultDto recordDto);

    ReceiveRollBackMemberPointDto convert(ReceiveRollBackMemberPointReq req);

    ConsumeMemberPointDto convert(ConsumeMemberPointReq req);

    ConsumeRollBackMemberPointDto convert(ConsumeRollBackMemberPointReq req);

    MemberRecordOPResultResp convert(MemberRecordOPResultDto opDto );

    ListMemberPointConsumeForBusinessDto convert(ListMemberPointConsumeForBusinessReq req);

    List<MemberPointsFlowInfoResp> convert(List<MemberPointDto> dtoList);

}
