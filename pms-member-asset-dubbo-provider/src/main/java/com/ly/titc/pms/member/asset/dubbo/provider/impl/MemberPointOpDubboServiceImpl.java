package com.ly.titc.pms.member.asset.dubbo.provider.impl;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.asset.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.enums.PonitReceiveTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointOpDubboService;
import com.ly.titc.pms.member.asset.dubbo.provider.converter.MemberPointConverter;
import com.ly.titc.pms.member.asset.mediator.entity.point.MemberRecordOPResultDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.ReceiveMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.point.ReceiveRollBackMemberPointDto;
import com.ly.titc.pms.member.asset.mediator.entity.store.MemberPointDto;
import com.ly.titc.pms.member.asset.mediator.service.MemberPointOpMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 16:32
 */
@Slf4j
@Validated
@DubboService
public class MemberPointOpDubboServiceImpl implements MemberPointOpDubboService {
    @Resource
    private MemberPointOpMedService  pointOpMedService;
    @Resource
    private MemberPointConverter converter;

    @Override
    public Response<MemberRecordOPResultResp> receive(ReceiveMemberPointReq req) {
        ReceiveMemberPointDto dto =converter.convert(req);
        dto.setReceiveType(PonitReceiveTypeEnum.RECEIVE.getType());
        if(req.getScore().compareTo(Constant.ZERO)<0){
           throw new ServiceException("获取积分数只能为正", RespCodeEnum.Store_Error_Rule.getCode());
        }
        MemberRecordOPResultDto opDto = pointOpMedService.receive(dto);
        return Response.success(converter.convert(opDto));
    }

    @Override
    public Response<MemberRecordOPResultResp> receiveRollback(ReceiveRollBackMemberPointReq req) {
        if(req.getScore().compareTo(Constant.ZERO)<0){
            throw new ServiceException("积分数量为正数", RespCodeEnum.Store_Error_Rule.getCode());
        }
        ReceiveRollBackMemberPointDto dto = converter.convert(req);
        MemberRecordOPResultDto opDto =pointOpMedService.receiveRollback(dto);
        return Response.success(converter.convert(opDto));
    }

    @Override
    public Response<MemberRecordOPResultResp> consume(ConsumeMemberPointReq req) {
        MemberRecordOPResultDto opDto = pointOpMedService.consume(converter.convert(req));
        return Response.success(converter.convert(opDto));
    }

    @Override
    public Response<MemberRecordOPResultResp> consumeRollback(ConsumeRollBackMemberPointReq req) {
        MemberRecordOPResultDto opDto = pointOpMedService.consumeRollback(converter.convert(req));
        return Response.success(converter.convert(opDto));
    }

    @Override
    public Response<List<MemberPointsFlowInfoResp>> listConsumeRecords(ListMemberPointConsumeForBusinessReq req) {
        List<MemberPointDto> pointDtos =  pointOpMedService.listConsumeRecords(converter.convert(req));
        return Response.success(converter.convert(pointDtos));
    }
}
