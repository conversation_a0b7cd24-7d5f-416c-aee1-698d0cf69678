<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 时间和日期 traceNo 线程 log级别 类名 方法名 消息内容-->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{apmTrace} [%c] [%M] %p %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <!-- 日志文件输出，供天网日志收集 -->
    <appender name="skynet" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/skynet-titc.dsf.dubbo.pms.member.asset/app/server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>/data/logs/skynet-titc.dsf.dubbo.pms.member.asset/app/member.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- each file should be at most 100MB, keep 60 days worth of history, but at most 20GB -->
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %thread %X{apmTrace} [%c] [%M] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 天网日志，仅记录业务代码中输出的日志 -->
    <logger name="com.ly.titc" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="skynet"/>
        <appender-ref ref="stdout"/>
    </logger>

    <!-- mybatis 控制台打印 SQL -->
    <!--<logger name="com.ly.zkt" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="stdout"/>
    </logger>-->

    <root>
        <level value="INFO"/>
        <appender-ref ref="stdout"/>
    </root>
</configuration>